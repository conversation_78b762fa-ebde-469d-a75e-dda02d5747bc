const Docker = require("dockerode");
const docker = new Docker({ socketPath: "/var/run/docker.sock" });
const path = require("path");
const fs = require("fs");

exports.createAndStartContainer = async (virtualObjectName, outputPath, sensorMappings, leshanIpAddress = 'localhost') => {
  try {
    console.log(`Creating container with name '${virtualObjectName}' and Leshan IP '${leshanIpAddress}'`);
    const sensorMappingsString = JSON.stringify(sensorMappings);

    // Get the container-specific directory path from the outputPath
    const containerDir = path.dirname(outputPath);

    // Check if the Docker image exists
    const images = await docker.listImages();
    const imageExists = images.some(image =>
      image.RepoTags && image.RepoTags.includes("custom-nodered-image:latest")
    );

    if (!imageExists) {
      throw new Error("Docker image 'custom-nodered-image' not found. Please build the image first.");
    }

    // Check if a container with this name already exists
    try {
      const existingContainer = await docker.getContainer(virtualObjectName);
      const containerInfo = await existingContainer.inspect();

      // If we get here, the container exists
      throw new Error(`A container with name '${virtualObjectName}' already exists. Container ID: ${containerInfo.Id}`);
    } catch (err) {
      // If the error is 'no such container', that's good - we can create a new one
      if (!err.message.includes('no such container')) {
        throw err; // Re-throw if it's a different error
      }
      // Otherwise continue with container creation
    }

    console.log(`Creating container with name: ${virtualObjectName} (explicitly set)`);
    const container = await docker.createContainer({
      Image: "custom-nodered-image",
      Env: [
        `VIRTUAL_OBJECT_NAME=${virtualObjectName}`,
        `SENSOR_MAPPINGS=${sensorMappingsString}`,
        `LESHAN_SERVER_HOST=${leshanIpAddress}`,
        `LESHAN_CLIENT_NAME=${virtualObjectName}`,
      ],
      name: virtualObjectName, // This is the Docker container name
      ExposedPorts: { "1880/tcp": {} },
      HostConfig: {
        PortBindings: { "1880/tcp": [{ HostPort: "" }] }, // Docker will assign a random available port
        Binds: [
          // Mount the container-specific directory to /data in the container
          `${containerDir}:/data`
        ]
      }
    });

    console.log(`Starting container: ${virtualObjectName}`);
    await container.start();

    console.log(`Inspecting container: ${virtualObjectName}`);
    const containerData = await container.inspect();

    return {
      id: container.id,
      ip: containerData.NetworkSettings.IPAddress,
      port: containerData.NetworkSettings.Ports["1880/tcp"]?.[0]?.HostPort,
      containerDir: containerDir
    };
  } catch (error) {
    console.error(`Error in createAndStartContainer for ${virtualObjectName}:`, error);
    // Add more context to the error before re-throwing
    if (error.message.includes('already in use')) {
      error.code = 'CONTAINER_NAME_CONFLICT';
    } else if (error.code === 'ECONNREFUSED') {
      error.message = `Could not connect to Docker daemon: ${error.message}`;
    }
    throw error;
  }
};

// Add cleanup function to remove container directory
exports.cleanupContainerDirectory = async (containerDir) => {
  try {
    if (fs.existsSync(containerDir)) {
      fs.rmSync(containerDir, { recursive: true, force: true });
      console.log(`Cleaned up container directory: ${containerDir}`);
    }
  } catch (error) {
    console.error(`Error cleaning up container directory ${containerDir}:`, error);
  }
};
