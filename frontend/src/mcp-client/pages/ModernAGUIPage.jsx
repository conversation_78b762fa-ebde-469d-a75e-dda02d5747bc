/**
 * Modern AG-UI Page - Full-screen professional interface
 * Standalone page for the modern agent UI experience
 */

import React, { useState, useCallback, useMemo } from 'react';
import { Box, CssBaseline, Drawer, AppBar, Toolbar, Typography, IconButton, Tabs, Tab } from '@mui/material';
import { Menu as MenuIcon, Close as CloseIcon } from '@mui/icons-material';
import ModernAGUIChatInterface from '../components/agui/ModernAGUIChatInterface';
import MCPToolsPanel from '../components/agui/MCPToolsPanel';
import MCPQueryComponent from '../components/agui/MCPQueryComponent';

const API_HOST = process.env.REACT_APP_API_HOST || 'localhost';
const API_BASE_URL = `http://${API_HOST}:5000`;

/**
 * Modern AG-UI Page Component - Full Screen
 */
const ModernAGUIPage = () => {
  const [agentConfig] = useState({
    agentId: 'modern-digital-twin-assistant',
    description: 'Modern Digital Twin Assistant with Professional UI',
    baseUrl: API_BASE_URL,
    initialState: {
      protocolVersion: '1.0',
      capabilities: ['sensor-data', 'real-time-monitoring', 'data-visualization', 'general-ai', 'filesystem-access'],
      connectionStatus: 'connected'
    }
  });

  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);

  // Handle state changes
  const handleStateChange = useCallback((state) => {
    console.log('[Modern AG-UI] Agent state changed:', state);
  }, []);

  // Handle new messages
  const handleMessageAdd = useCallback((message) => {
    console.log('[Modern AG-UI] New message:', message);
  }, []);

  // Handle tool execution from tools panel
  const handleToolExecute = useCallback((toolExecution) => {
    console.log('[Modern AG-UI] Tool executed:', toolExecution);
    // You can add the tool result to the chat or handle it as needed
  }, []);

  // Memoize tools and context to prevent re-initialization
  const tools = useMemo(() => [
    { name: 'query-data', description: 'Query sensor data from InfluxDB' },
    { name: 'write-data', description: 'Write data to InfluxDB' },
    { name: 'read_file', description: 'Read files from filesystem' },
    { name: 'write_file', description: 'Write files to filesystem' },
    { name: 'list_directory', description: 'List directory contents' },
    { name: 'get-status', description: 'Get system status' },
    { name: 'analyze-trends', description: 'Analyze data trends' }
  ], []);

  const context = useMemo(() => ({
    protocolVersion: '1.0',
    environment: 'production',
    capabilities: ['streaming', 'tool-calls', 'state-management', 'filesystem-access']
  }), []);

  return (
    <Box sx={{
      height: '100vh',
      width: '100vw',
      display: 'flex',
      flexDirection: 'column',
      bgcolor: 'background.default'
    }}>
      <CssBaseline />

      {/* App Bar */}
      <AppBar position="static" elevation={1}>
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            onClick={() => setSidebarOpen(true)}
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Digital Twin AI Assistant
          </Typography>
          <Tabs
            value={activeTab}
            onChange={(e, newValue) => setActiveTab(newValue)}
            textColor="inherit"
            indicatorColor="secondary"
          >
            <Tab label="Chat" />
            <Tab label="Quick Query" />
            <Tab label="Tools" />
          </Tabs>
        </Toolbar>
      </AppBar>

      {/* Main Content */}
      <Box sx={{ display: 'flex', flex: 1, overflow: 'hidden' }}>
        {/* Sidebar Drawer */}
        <Drawer
          anchor="left"
          open={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          sx={{
            '& .MuiDrawer-paper': {
              width: 400,
              boxSizing: 'border-box',
            },
          }}
        >
          <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">MCP Tools</Typography>
            <IconButton onClick={() => setSidebarOpen(false)}>
              <CloseIcon />
            </IconButton>
          </Box>
          <Box sx={{ flex: 1, overflow: 'auto', px: 2 }}>
            <MCPToolsPanel onToolExecute={handleToolExecute} />
          </Box>
        </Drawer>

        {/* Main Content Area */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          {activeTab === 0 && (
            <ModernAGUIChatInterface
              agentConfig={agentConfig}
              tools={tools}
              context={context}
              onStateChange={handleStateChange}
              onMessageAdd={handleMessageAdd}
            />
          )}

          {activeTab === 1 && (
            <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
              <MCPQueryComponent tools={tools} />
            </Box>
          )}

          {activeTab === 2 && (
            <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
              <MCPToolsPanel onToolExecute={handleToolExecute} />
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default ModernAGUIPage;
