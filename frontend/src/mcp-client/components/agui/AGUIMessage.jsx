/**
 * AG-UI Message Component - Modern AI Chat Interface
 * Futuristic design inspired by <PERSON><PERSON><PERSON> and ChatGPT
 */

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Avatar,
  Paper,
  IconButton,
  Tooltip,
  Chip,
  Collapse,
  Alert,
  Fade,
  useTheme,
  alpha
} from '@mui/material';
import {
  SmartToy as BotIcon,
  Person as PersonIcon,
  ContentCopy as CopyIcon,
  Check as CheckIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Code as CodeIcon,
  DataObject as DataIcon,
  AccessTime as TimeIcon,
  AutoAwesome as AIIcon,
  Psychology as NeuralIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { formatDistanceToNow } from 'date-fns';

import Markdown from 'markdown-to-jsx';
import AGUIDataStandardizer from './AGUIDataStandardizer';

// Enhanced Formatted Text Renderer with Modern Styling
const FormattedTextRenderer = ({ content }) => {
  const theme = useTheme();
  
  if (!content) return null;

  // Modern markdown options with futuristic styling
  const markdownOptions = {
    overrides: {
      h1: {
        component: Typography,
        props: {
          variant: 'h4',
          component: 'h1',
          sx: { 
            mt: 3, 
            mb: 2, 
            fontWeight: 700,
            background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            letterSpacing: '-0.02em'
          }
        }
      },
      h2: {
        component: Typography,
        props: {
          variant: 'h5',
          component: 'h2',
          sx: { 
            mt: 2.5, 
            mb: 1.5, 
            fontWeight: 600,
            color: 'primary.main',
            letterSpacing: '-0.01em'
          }
        }
      },
      h3: {
        component: Typography,
        props: {
          variant: 'h6',
          component: 'h3',
          sx: { 
            mt: 2, 
            mb: 1, 
            fontWeight: 600,
            color: 'primary.main'
          }
        }
      },
      p: {
        component: Typography,
        props: {
          variant: 'body1',
          component: 'p',
          sx: { 
            mb: 1.5, 
            lineHeight: 1.7,
            fontSize: '0.95rem',
            color: 'text.primary'
          }
        }
      },
      strong: {
        component: Typography,
        props: {
          component: 'strong',
          sx: { 
            fontWeight: 700, 
            color: 'text.primary',
            background: alpha(theme.palette.primary.main, 0.1),
            px: 0.5,
            py: 0.1,
            borderRadius: 0.5
          }
        }
      },
      em: {
        component: Typography,
        props: {
          component: 'em',
          sx: { 
            fontStyle: 'italic', 
            color: 'text.secondary',
            fontSize: '0.95rem'
          }
        }
      },
      ul: {
        component: Box,
        props: {
          component: 'ul',
          sx: { 
            pl: 3, 
            mb: 1.5,
            '& li': {
              mb: 0.8,
              position: 'relative',
              '&::marker': {
                color: theme.palette.primary.main,
                fontSize: '1.2em'
              }
            }
          }
        }
      },
      ol: {
        component: Box,
        props: {
          component: 'ol',
          sx: { 
            pl: 3, 
            mb: 1.5,
            '& li': {
              mb: 0.8,
              '&::marker': {
                color: theme.palette.primary.main,
                fontWeight: 600
              }
            }
          }
        }
      },
      li: {
        component: Typography,
        props: {
          component: 'li',
          variant: 'body1',
          sx: { 
            lineHeight: 1.6,
            fontSize: '0.95rem'
          }
        }
      },
      blockquote: {
        component: Paper,
        props: {
          elevation: 0,
          sx: {
            p: 2.5,
            mb: 2,
            borderLeft: 4,
            borderLeftColor: 'primary.main',
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.03)}, ${alpha(theme.palette.secondary.main, 0.03)})`,
            backdropFilter: 'blur(10px)',
            borderRadius: 2,
            fontStyle: 'italic',
            position: 'relative',
            '&::before': {
              content: '"❝"',
              position: 'absolute',
              top: 8,
              left: 8,
              fontSize: '1.5rem',
              color: theme.palette.primary.main,
              opacity: 0.5
            }
          }
        }
      },
      code: {
        component: Chip,
        props: {
          size: 'small',
          sx: {
            fontFamily: 'JetBrains Mono, Monaco, Menlo, "Ubuntu Mono", monospace',
            fontSize: '0.8rem',
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.secondary.main, 0.1)})`,
            color: 'primary.main',
            borderRadius: 1.5,
            height: 'auto',
            py: 0.5,
            px: 1,
            fontWeight: 500,
            border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
          }
        }
      },
      pre: {
        component: CustomCodeBlock
      }
    }
  };

  return (
    <Box sx={{ '& > *:first-child': { mt: 0 } }}>
      <Markdown options={markdownOptions}>
        {content}
      </Markdown>
    </Box>
  );
};

// Enhanced Custom Code Block Component
const CustomCodeBlock = ({ children, ...props }) => {
  const codeElement = React.Children.toArray(children).find(
    child => child.type === 'code' || (child.props && child.props.className)
  );
  
  if (!codeElement) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Paper
          elevation={0}
          sx={{
            p: 3,
            mb: 2,
            background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',
            borderRadius: 3,
            overflow: 'auto',
            border: '1px solid rgba(255, 255, 255, 0.1)'
          }}
        >
          <Typography
            component="pre"
            sx={{
              fontFamily: 'JetBrains Mono, Monaco, Menlo, "Ubuntu Mono", monospace',
              fontSize: '0.875rem',
              color: '#e8eaed',
              margin: 0,
              whiteSpace: 'pre-wrap'
            }}
          >
            {children}
          </Typography>
        </Paper>
      </motion.div>
    );
  }

  const className = codeElement.props?.className || '';
  const language = className.replace('lang-', '') || 'text';
  const content = typeof codeElement.props?.children === 'string' 
    ? codeElement.props.children 
    : String(children);

  return (
    <EnhancedCodeBlock
      language={language}
      content={content}
      sx={{ mb: 2 }}
    />
  );
};

// Enhanced Code Block with Modern Design
const EnhancedCodeBlock = ({ language, content, sx = {} }) => {
  const [copied, setCopied] = React.useState(false);
  const theme = useTheme();

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  const getLanguageConfig = (lang) => {
    const configs = {
      javascript: { color: '#f7df1e', icon: '🟨', bgGradient: 'linear-gradient(135deg, #f7df1e20, #f7df1e10)' },
      python: { color: '#3776ab', icon: '🐍', bgGradient: 'linear-gradient(135deg, #3776ab20, #3776ab10)' },
      sql: { color: '#336791', icon: '🗄️', bgGradient: 'linear-gradient(135deg, #33679120, #33679110)' },
      json: { color: '#000000', icon: '📋', bgGradient: 'linear-gradient(135deg, #00000020, #00000010)' },
      html: { color: '#e34c26', icon: '🌐', bgGradient: 'linear-gradient(135deg, #e34c2620, #e34c2610)' },
      css: { color: '#1572b6', icon: '🎨', bgGradient: 'linear-gradient(135deg, #1572b620, #1572b610)' },
      bash: { color: '#4eaa25', icon: '⚡', bgGradient: 'linear-gradient(135deg, #4eaa2520, #4eaa2510)' },
      shell: { color: '#4eaa25', icon: '⚡', bgGradient: 'linear-gradient(135deg, #4eaa2520, #4eaa2510)' }
    };
    return configs[lang.toLowerCase()] || { color: '#6B46C1', icon: '📄', bgGradient: 'linear-gradient(135deg, #6B46C120, #6B46C110)' };
  };

  const config = getLanguageConfig(language);

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      whileHover={{ scale: 1.001 }}
    >
      <Paper
        elevation={0}
        sx={{
          background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',
          border: `1px solid ${alpha(config.color, 0.3)}`,
          borderRadius: 3,
          overflow: 'hidden',
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '1px',
            background: config.bgGradient
          },
          ...sx
        }}
      >
        {/* Modern Header */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            px: 3,
            py: 1.5,
            background: config.bgGradient,
            borderBottom: `1px solid ${alpha(config.color, 0.2)}`
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
            <Typography variant="body2" sx={{ fontSize: '1.1rem' }}>
              {config.icon}
            </Typography>
            <Chip
              label={language.toUpperCase()}
              size="small"
              sx={{
                backgroundColor: alpha(config.color, 0.2),
                color: config.color,
                border: `1px solid ${alpha(config.color, 0.4)}`,
                fontWeight: 700,
                height: 24,
                fontSize: '0.7rem',
                letterSpacing: '0.5px'
              }}
            />
          </Box>

          <Tooltip title={copied ? "Copied!" : "Copy code"} arrow>
            <IconButton
              size="small"
              onClick={handleCopy}
              sx={{
                                 color: '#ffffff',
                 backgroundColor: alpha('#ffffff', 0.05),
                 backdropFilter: 'blur(10px)',
                 border: '1px solid rgba(255, 255, 255, 0.1)',
                 '&:hover': { 
                   backgroundColor: alpha('#ffffff', 0.1),
                  transform: 'scale(1.05)'
                },
                transition: 'all 0.2s ease'
              }}
            >
              <AnimatePresence mode="wait">
                {copied ? (
                  <motion.div
                    key="check"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0 }}
                  >
                    <CheckIcon fontSize="small" sx={{ color: '#4caf50' }} />
                  </motion.div>
                ) : (
                  <motion.div
                    key="copy"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0 }}
                  >
                    <CopyIcon fontSize="small" />
                  </motion.div>
                )}
              </AnimatePresence>
            </IconButton>
          </Tooltip>
        </Box>

        {/* Enhanced Code Content */}
        <Box sx={{ p: 3, backgroundColor: '#1e1e1e' }}>
          <Typography
            component="pre"
            variant="body2"
            sx={{
              fontFamily: 'JetBrains Mono, Monaco, Menlo, "Ubuntu Mono", monospace',
              fontSize: '0.875rem',
              lineHeight: 1.6,
              color: '#e8eaed',
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word',
              margin: 0,
              overflow: 'auto'
            }}
          >
            {content}
          </Typography>
        </Box>
      </Paper>
    </motion.div>
  );
};

// Modern AI Avatar Component
const AIAvatar = ({ isStreaming = false }) => {
  const theme = useTheme();
  
  return (
    <motion.div
      animate={isStreaming ? { 
        boxShadow: [
          `0 0 20px ${alpha(theme.palette.primary.main, 0.3)}`,
          `0 0 30px ${alpha(theme.palette.primary.main, 0.5)}`,
          `0 0 20px ${alpha(theme.palette.primary.main, 0.3)}`
        ]
      } : {}}
      transition={{ duration: 2, repeat: isStreaming ? Infinity : 0 }}
    >
      <Avatar
        sx={{
          mr: 2,
          width: 44,
          height: 44,
          background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
          border: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,
          backdropFilter: 'blur(10px)',
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            inset: -2,
            background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
            borderRadius: '50%',
            opacity: 0.3,
            filter: 'blur(8px)',
            zIndex: -1
          }
        }}
      >
        <motion.div
          animate={isStreaming ? { rotate: 360 } : {}}
          transition={{ duration: 3, repeat: isStreaming ? Infinity : 0, ease: "linear" }}
        >
          <AIIcon sx={{ fontSize: '1.4rem' }} />
        </motion.div>
      </Avatar>
    </motion.div>
  );
};

// Enhanced User Avatar
const UserAvatar = () => {
  const theme = useTheme();
  
  return (
    <Avatar
      sx={{
        ml: 2,
        width: 40,
        height: 40,
        background: `linear-gradient(135deg, ${theme.palette.grey[600]}, ${theme.palette.grey[800]})`,
        border: `2px solid ${alpha(theme.palette.grey[500], 0.3)}`
      }}
    >
      <PersonIcon sx={{ fontSize: '1.2rem' }} />
    </Avatar>
  );
};

/**
 * Modern AG-UI Message Component
 */
const AGUIMessage = ({
  message,
  agentState = {},
  isStreaming = false,
  onFeedback
}) => {
  const [showMetadata, setShowMetadata] = useState(false);
  const [copied, setCopied] = useState(false);
  const theme = useTheme();

  const handleCopy = () => {
    const textToCopy = typeof message.content === 'string'
      ? message.content
      : typeof message.content === 'object'
        ? JSON.stringify(message.content, null, 2)
        : String(message.content || 'No content');

    navigator.clipboard.writeText(textToCopy).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  const renderStreamingIndicator = () => (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
        <Box
          sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          mt: 2,
          p: 3,
          borderRadius: 3,
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.secondary.main, 0.05)})`,
          border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
          backdropFilter: 'blur(10px)',
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '2px',
            background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
            animation: 'shimmer 2s infinite',
            '@keyframes shimmer': {
              '0%': { transform: 'translateX(-100%)' },
              '100%': { transform: 'translateX(100%)' }
            }
          }
        }}
      >
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        >
          <NeuralIcon sx={{ color: 'primary.main', fontSize: '1.2rem' }} />
        </motion.div>
        <Typography variant="body2" color="primary" sx={{ fontWeight: 500 }}>
          AI is thinking...
        </Typography>
        <Box sx={{ flexGrow: 1 }} />
        <motion.div
          animate={{ opacity: [0.3, 1, 0.3] }}
          transition={{ duration: 1.5, repeat: Infinity }}
        >
          <Typography variant="body2" color="text.secondary">
            ●●●
      </Typography>
        </motion.div>
    </Box>
    </motion.div>
  );

  const renderMessageContent = () => {
    // Ensure content is always a string
    const contentString = typeof message.content === 'string'
      ? message.content
      : typeof message.content === 'object'
        ? JSON.stringify(message.content, null, 2)
        : String(message.content || '');
    
    // Handle streaming messages
    if (isStreaming) {
      return (
        <Box>
          <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
            {contentString}
            <Box
              component="span"
              sx={{
                display: 'inline-block',
                width: '2px',
                height: '1.2em',
                backgroundColor: 'primary.main',
                animation: 'blink 1s infinite',
                ml: 0.5,
                '@keyframes blink': {
                  '0%, 50%': { opacity: 1 },
                  '51%, 100%': { opacity: 0 }
                }
              }}
            />
          </Typography>
          {!contentString && renderStreamingIndicator()}
        </Box>
      );
    }

    // For completed messages, use appropriate renderer
    if (message.role === 'assistant') {
      return (
        <Box>
          {/* Choose ONLY ONE renderer - either data visualization or formatted text */}
          {message.toolResults && message.toolResults.length > 0 ? (
            // Function call detected - show data visualization
            <AGUIDataStandardizer
              agentState={{
                dataAvailable: true,
                toolResults: message.toolResults,
                rawData: message.data,
                metadata: message.metadata
              }}
              onFeedback={onFeedback}
            />
          ) : (
            // No function calls - format text with code blocks, links, etc.
            <FormattedTextRenderer content={contentString} />
          )}
        </Box>
      );
    } else {
      // User messages - simple text
      return (
        <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
          {contentString}
        </Typography>
      );
    }
  };

  const getMessageColor = () => {
    if (message.role === 'user') return 'primary.main';
    if (isStreaming) return 'rgba(25, 118, 210, 0.04)';
    return 'white';
  };

  const formatTimestamp = (timestamp) => {
    try {
      const date = new Date(timestamp);
      return formatDistanceToNow(date, { addSuffix: true });
    } catch (e) {
      return 'Unknown time';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start',
          mb: 3,
          position: 'relative'
        }}
      >
        {message.role === 'assistant' && (
          <AIAvatar isStreaming={isStreaming} />
        )}

        <Box
          sx={{
            maxWidth: { xs: '90%', sm: '85%', md: '80%' },
            minWidth: 200,
            position: 'relative'
          }}
        >
          {/* Modern Message Header */}
          <motion.div
            initial={{ opacity: 0, x: message.role === 'user' ? 20 : -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: message.role === 'user' ? 'flex-end' : 'flex-start',
              mb: 1,
              gap: 1.5
          }}>
              <Typography 
                variant="caption" 
                sx={{ 
                  color: 'text.secondary',
                  fontWeight: 600,
                  letterSpacing: '0.5px',
                  textTransform: 'uppercase',
                  fontSize: '0.7rem'
                }}
              >
                {message.role === 'user' ? 'You' : 'AI Assistant'}
            </Typography>

            {message.timestamp && (
              <Chip
                icon={<TimeIcon />}
                label={formatTimestamp(message.timestamp)}
                size="small"
                  sx={{ 
                    fontSize: '0.65rem', 
                    height: 22,
                    backgroundColor: alpha(theme.palette.grey[500], 0.1),
                    border: `1px solid ${alpha(theme.palette.grey[500], 0.2)}`,
                    '& .MuiChip-icon': { fontSize: '0.8rem' }
                  }}
              />
            )}

            {isStreaming && (
                <motion.div
                  animate={{ scale: [1, 1.05, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
              <Chip
                label="Streaming"
                size="small"
                    sx={{ 
                      fontSize: '0.65rem', 
                      height: 22,
                                             background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                       color: '#ffffff',
                      border: 'none',
                      fontWeight: 600
                    }}
              />
                </motion.div>
            )}

            {message.role === 'assistant' && !isStreaming && (
                <Tooltip title={copied ? 'Copied!' : 'Copy message'} arrow>
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                  >
                <IconButton
                  size="small"
                  onClick={handleCopy}
                      sx={{ 
                        opacity: 0.6,
                        backgroundColor: alpha(theme.palette.primary.main, 0.05),
                        border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                        '&:hover': { 
                          opacity: 1,
                          backgroundColor: alpha(theme.palette.primary.main, 0.1),
                          transform: 'scale(1.05)'
                        },
                        transition: 'all 0.2s ease'
                      }}
                >
                      <motion.div
                        animate={copied ? { scale: [1, 1.3, 1] } : {}}
                        transition={{ duration: 0.3 }}
                      >
                        {copied ? (
                          <CheckIcon fontSize="small" sx={{ color: 'success.main' }} />
                        ) : (
                  <CopyIcon fontSize="small" />
                        )}
                      </motion.div>
                </IconButton>
                  </motion.div>
              </Tooltip>
            )}
          </Box>
          </motion.div>

          {/* Modern Message Content */}
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.4, delay: 0.2 }}
            whileHover={{ scale: 1.002 }}
          >
          <Paper
              elevation={0}
            sx={{
                p: { xs: 2.5, sm: 3 },
                borderRadius: 4,
              position: 'relative',
                overflow: 'hidden',
                ...(message.role === 'user' ? {
                  // User message styling - Modern gradient
                                   background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                 color: '#ffffff',
                  border: `1px solid ${alpha(theme.palette.primary.light, 0.3)}`,
                  boxShadow: `0 8px 32px ${alpha(theme.palette.primary.main, 0.3)}`,
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: `linear-gradient(135deg, ${alpha('#ffffff', 0.1)}, transparent)`,
                    pointerEvents: 'none'
                  }
                } : {
                  // Assistant message styling - Glassmorphic design
                  background: isStreaming 
                    ? `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)}, ${alpha(theme.palette.background.paper, 0.95)})`
                    : `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)}, ${alpha(theme.palette.background.paper, 0.95)})`,
                  backdropFilter: 'blur(20px)',
                  border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                  boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.1)}`,
                  color: 'text.primary'
                }),
              ...(isStreaming && {
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                    height: '3px',
                  width: '100%',
                    background: `linear-gradient(90deg, transparent, ${theme.palette.primary.main}, ${theme.palette.secondary.main}, transparent)`,
                    animation: 'shimmerLoading 2s infinite',
                    borderRadius: '0 0 16px 16px'
                },
                  '@keyframes shimmerLoading': {
                  '0%': { transform: 'translateX(-100%)' },
                  '100%': { transform: 'translateX(100%)' }
                }
                }),
                // Hover effects
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: message.role === 'user' 
                    ? `0 12px 40px ${alpha(theme.palette.primary.main, 0.4)}`
                    : `0 12px 40px ${alpha(theme.palette.common.black, 0.15)}`
                }
            }}
          >
              {/* Content with enhanced styling */}
              <Box sx={{ position: 'relative', zIndex: 1 }}>
            {renderMessageContent()}
              </Box>
              
              {/* Subtle pattern overlay for assistant messages */}
              {message.role === 'assistant' && (
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    opacity: 0.03,
                    background: `radial-gradient(circle at 20% 50%, ${theme.palette.primary.main} 1px, transparent 1px),
                                radial-gradient(circle at 80% 50%, ${theme.palette.secondary.main} 1px, transparent 1px)`,
                    backgroundSize: '20px 20px',
                    pointerEvents: 'none'
                  }}
                />
              )}
          </Paper>
          </motion.div>

          {/* Metadata */}
          {(message.metadata || message.toolResults || message.data) && !isStreaming && (
            <Box sx={{ mt: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <IconButton
                  size="small"
                  onClick={() => setShowMetadata(!showMetadata)}
                  sx={{ opacity: 0.6 }}
                >
                  {showMetadata ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
                <Typography variant="caption" color="text.secondary">
                  AG-UI Metadata
                </Typography>

                {message.toolResults && (
                  <Chip
                    icon={<CodeIcon />}
                    label={`${message.toolResults.length} tools`}
                    size="small"
                    variant="outlined"
                    sx={{ fontSize: '0.7rem' }}
                  />
                )}

                {message.data && (
                  <Chip
                    icon={<DataIcon />}
                    label="Data"
                    size="small"
                    variant="outlined"
                    sx={{ fontSize: '0.7rem' }}
                  />
                )}
              </Box>

              <Collapse in={showMetadata}>
                <Paper variant="outlined" sx={{ p: 1, mt: 1, bgcolor: 'grey.50' }}>
                  {message.metadata && (
                    <Box sx={{ mb: 1 }}>
                      <Typography variant="caption" color="text.secondary" gutterBottom>
                        Metadata:
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {Object.entries(message.metadata).map(([key, value]) => {
                          // Safely convert value to string
                          const safeValue = typeof value === 'string'
                            ? value
                            : typeof value === 'object'
                              ? JSON.stringify(value)
                              : String(value);

                          return (
                            <Chip
                              key={key}
                              label={`${key}: ${safeValue.slice(0, 30)}${safeValue.length > 30 ? '...' : ''}`}
                              size="small"
                              variant="outlined"
                              sx={{ fontSize: '0.7rem' }}
                            />
                          );
                        })}
                      </Box>
                    </Box>
                  )}

                  {message.toolResults && (
                    <Box sx={{ mb: 1 }}>
                      <Typography variant="caption" color="text.secondary" gutterBottom>
                        Tool Results:
                      </Typography>
                      {message.toolResults.map((tool, index) => {
                        // Safely convert tool.result to string
                        let resultText = 'Executed';
                        if (tool.result) {
                          if (typeof tool.result === 'string') {
                            resultText = tool.result;
                          } else if (typeof tool.result === 'object') {
                            // Handle objects with content property
                            if (tool.result.content) {
                              if (typeof tool.result.content === 'string') {
                                resultText = tool.result.content;
                              } else if (Array.isArray(tool.result.content)) {
                                resultText = tool.result.content.map(item =>
                                  typeof item === 'string' ? item :
                                  typeof item === 'object' && item.text ? item.text :
                                  JSON.stringify(item)
                                ).join(' ');
                              } else {
                                resultText = JSON.stringify(tool.result.content);
                              }
                            } else {
                              resultText = JSON.stringify(tool.result);
                            }
                          } else {
                            resultText = String(tool.result);
                          }
                        }

                        return (
                          <Alert key={index} severity="info" sx={{ mt: 0.5, fontSize: '0.8rem' }}>
                            <strong>{tool.name}</strong>: {resultText}
                          </Alert>
                        );
                      })}
                    </Box>
                  )}

                  {agentState && Object.keys(agentState).length > 0 && (
                    <Box>
                      <Typography variant="caption" color="text.secondary" gutterBottom>
                        Agent State:
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {Object.entries(agentState).slice(0, 5).map(([key, value]) => (
                          <Chip
                            key={key}
                            label={`${key}: ${String(value).slice(0, 20)}${String(value).length > 20 ? '...' : ''}`}
                            size="small"
                            variant="outlined"
                            sx={{ fontSize: '0.7rem' }}
                          />
                        ))}
                      </Box>
                    </Box>
                  )}
                </Paper>
              </Collapse>
            </Box>
          )}
        </Box>

        {message.role === 'user' && (
          <UserAvatar />
        )}
      </Box>
    </motion.div>
  );
};

export default AGUIMessage;
