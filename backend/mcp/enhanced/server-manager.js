/**
 * Enhanced MCP Server Manager
 * Manages multiple MCP servers with health monitoring, load balancing, and failover
 */

const { Client } = require("@modelcontextprotocol/sdk/client/index.js");
const { StdioClientTransport } = require("@modelcontextprotocol/sdk/client/stdio.js");
const EventEmitter = require('events');

class MCPServerManager extends EventEmitter {
  constructor() {
    super();
    this.servers = new Map();
    this.activeServers = new Set();
    this.healthCheckInterval = null;
    this.loadBalancingStrategy = 'round-robin';
    this.currentServerIndex = 0;
  }

  /**
   * Register a new MCP server
   * @param {Object} config - Server configuration
   * @param {string} config.id - Unique server identifier
   * @param {string} config.name - Human-readable server name
   * @param {string} config.command - Command to start the server
   * @param {Array} config.args - Command arguments
   * @param {Object} config.env - Environment variables
   * @param {Array} config.capabilities - Server capabilities
   * @param {number} config.priority - Server priority (higher = preferred)
   */
  async registerServer(config) {
    const {
      id,
      name,
      command,
      args = [],
      env = {},
      capabilities = [],
      priority = 1,
      healthCheckInterval = 30000
    } = config;

    if (this.servers.has(id)) {
      throw new Error(`Server with ID '${id}' is already registered`);
    }

    const serverInfo = {
      id,
      name,
      command,
      args,
      env: { ...process.env, ...env },
      capabilities,
      priority,
      healthCheckInterval,
      client: null,
      transport: null,
      isConnected: false,
      lastHealthCheck: null,
      connectionAttempts: 0,
      maxConnectionAttempts: 3,
      tools: [],
      metadata: {
        registeredAt: new Date(),
        lastConnectedAt: null,
        totalQueries: 0,
        successfulQueries: 0,
        failedQueries: 0
      }
    };

    this.servers.set(id, serverInfo);
    console.log(`Registered MCP server: ${name} (${id})`);

    // Attempt to connect
    await this.connectServer(id);
    
    return serverInfo;
  }

  /**
   * Connect to a specific server
   * @param {string} serverId - Server ID
   */
  async connectServer(serverId) {
    const server = this.servers.get(serverId);
    if (!server) {
      throw new Error(`Server '${serverId}' not found`);
    }

    if (server.isConnected) {
      console.log(`Server '${serverId}' is already connected`);
      return true;
    }

    try {
      console.log(`Connecting to MCP server: ${server.name}`);

      // Create client
      server.client = new Client({
        name: `Enhanced MCP Client - ${server.name}`,
        version: "1.0.0"
      });

      // Create transport
      server.transport = new StdioClientTransport({
        command: server.command,
        args: server.args,
        env: server.env
      });

      // Set up error handler
      server.client.onerror = (error) => {
        console.error(`MCP server '${serverId}' error:`, error);
        this.handleServerError(serverId, error);
      };

      // Connect
      await server.client.connect(server.transport);

      // Get available tools
      const toolsResponse = await server.client.listTools();
      server.tools = toolsResponse.tools || [];

      // Update server status
      server.isConnected = true;
      server.connectionAttempts = 0;
      server.metadata.lastConnectedAt = new Date();
      this.activeServers.add(serverId);

      console.log(`Successfully connected to MCP server: ${server.name}`);
      console.log(`Available tools: ${server.tools.map(t => t.name).join(', ')}`);

      this.emit('serverConnected', { serverId, server });
      return true;

    } catch (error) {
      console.error(`Failed to connect to MCP server '${serverId}':`, error);
      server.connectionAttempts++;
      server.isConnected = false;
      this.activeServers.delete(serverId);
      
      this.emit('serverConnectionFailed', { serverId, server, error });
      
      if (server.connectionAttempts >= server.maxConnectionAttempts) {
        console.error(`Max connection attempts reached for server '${serverId}'`);
        this.emit('serverMaxAttemptsReached', { serverId, server });
      }
      
      return false;
    }
  }

  /**
   * Disconnect from a specific server
   * @param {string} serverId - Server ID
   */
  async disconnectServer(serverId) {
    const server = this.servers.get(serverId);
    if (!server) {
      throw new Error(`Server '${serverId}' not found`);
    }

    if (server.client && server.isConnected) {
      try {
        await server.client.close();
      } catch (error) {
        console.error(`Error closing server '${serverId}':`, error);
      }
    }

    server.isConnected = false;
    server.client = null;
    server.transport = null;
    this.activeServers.delete(serverId);

    console.log(`Disconnected from MCP server: ${server.name}`);
    this.emit('serverDisconnected', { serverId, server });
  }

  /**
   * Get the best available server for a query
   * @param {Array} requiredCapabilities - Required server capabilities
   * @returns {Object|null} Server info or null if none available
   */
  getAvailableServer(requiredCapabilities = []) {
    const availableServers = Array.from(this.activeServers)
      .map(id => this.servers.get(id))
      .filter(server => {
        if (!server.isConnected) return false;
        
        // Check if server has required capabilities
        if (requiredCapabilities.length > 0) {
          return requiredCapabilities.every(cap => 
            server.capabilities.includes(cap)
          );
        }
        
        return true;
      })
      .sort((a, b) => b.priority - a.priority); // Sort by priority (descending)

    if (availableServers.length === 0) {
      return null;
    }

    // Load balancing strategy
    switch (this.loadBalancingStrategy) {
      case 'round-robin':
        const server = availableServers[this.currentServerIndex % availableServers.length];
        this.currentServerIndex++;
        return server;
      
      case 'priority':
        return availableServers[0]; // Highest priority
      
      case 'least-loaded':
        return availableServers.reduce((least, current) => 
          current.metadata.totalQueries < least.metadata.totalQueries ? current : least
        );
      
      default:
        return availableServers[0];
    }
  }

  /**
   * Call a tool on the best available server
   * @param {string} toolName - Tool name
   * @param {Object} params - Tool parameters
   * @param {Array} requiredCapabilities - Required server capabilities
   * @returns {Promise<Object>} Tool result
   */
  async callTool(toolName, params, requiredCapabilities = []) {
    const server = this.getAvailableServer(requiredCapabilities);
    
    if (!server) {
      throw new Error('No available MCP servers for this request');
    }

    // Check if server has the requested tool
    const tool = server.tools.find(t => t.name === toolName);
    if (!tool) {
      throw new Error(`Tool '${toolName}' not available on server '${server.id}'`);
    }

    try {
      server.metadata.totalQueries++;
      
      const result = await server.client.callTool({
        name: toolName,
        arguments: params
      });

      server.metadata.successfulQueries++;
      
      return {
        result,
        serverId: server.id,
        serverName: server.name,
        metadata: {
          toolName,
          executionTime: Date.now(),
          serverId: server.id
        }
      };

    } catch (error) {
      server.metadata.failedQueries++;
      console.error(`Tool call failed on server '${server.id}':`, error);
      
      // Try failover to another server
      const fallbackServer = this.getAvailableServer(requiredCapabilities);
      if (fallbackServer && fallbackServer.id !== server.id) {
        console.log(`Attempting failover to server '${fallbackServer.id}'`);
        return this.callTool(toolName, params, requiredCapabilities);
      }
      
      throw error;
    }
  }

  /**
   * Get all available tools from all connected servers
   * @returns {Array} Combined list of tools with server information
   */
  getAllTools() {
    const allTools = [];
    
    for (const [serverId, server] of this.servers) {
      if (server.isConnected && server.tools) {
        server.tools.forEach(tool => {
          allTools.push({
            ...tool,
            serverId,
            serverName: server.name,
            serverCapabilities: server.capabilities
          });
        });
      }
    }
    
    return allTools;
  }

  /**
   * Get server statistics
   * @returns {Object} Server statistics
   */
  getServerStats() {
    const stats = {
      totalServers: this.servers.size,
      activeServers: this.activeServers.size,
      inactiveServers: this.servers.size - this.activeServers.size,
      totalTools: this.getAllTools().length,
      servers: {}
    };

    for (const [serverId, server] of this.servers) {
      stats.servers[serverId] = {
        id: server.id,
        name: server.name,
        isConnected: server.isConnected,
        capabilities: server.capabilities,
        toolCount: server.tools.length,
        metadata: server.metadata
      };
    }

    return stats;
  }

  /**
   * Start health monitoring for all servers
   */
  startHealthMonitoring() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.healthCheckInterval = setInterval(async () => {
      for (const [serverId, server] of this.servers) {
        if (server.isConnected) {
          try {
            // Simple health check - list tools
            await server.client.listTools();
            server.lastHealthCheck = new Date();
          } catch (error) {
            console.warn(`Health check failed for server '${serverId}':`, error);
            this.handleServerError(serverId, error);
          }
        } else if (server.connectionAttempts < server.maxConnectionAttempts) {
          // Try to reconnect
          console.log(`Attempting to reconnect to server '${serverId}'`);
          await this.connectServer(serverId);
        }
      }
    }, 30000); // Check every 30 seconds

    console.log('Started health monitoring for MCP servers');
  }

  /**
   * Stop health monitoring
   */
  stopHealthMonitoring() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      console.log('Stopped health monitoring for MCP servers');
    }
  }

  /**
   * Handle server errors
   * @param {string} serverId - Server ID
   * @param {Error} error - Error object
   */
  handleServerError(serverId, error) {
    const server = this.servers.get(serverId);
    if (server) {
      server.isConnected = false;
      this.activeServers.delete(serverId);
      this.emit('serverError', { serverId, server, error });
    }
  }

  /**
   * Shutdown all servers
   */
  async shutdown() {
    console.log('Shutting down MCP Server Manager...');
    
    this.stopHealthMonitoring();
    
    const disconnectPromises = Array.from(this.servers.keys()).map(serverId =>
      this.disconnectServer(serverId).catch(error =>
        console.error(`Error disconnecting server '${serverId}':`, error)
      )
    );
    
    await Promise.all(disconnectPromises);
    
    this.servers.clear();
    this.activeServers.clear();
    
    console.log('MCP Server Manager shutdown complete');
  }
}

module.exports = MCPServerManager;
