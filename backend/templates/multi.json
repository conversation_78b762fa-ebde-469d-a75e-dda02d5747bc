[{"id": "flow1", "type": "tab", "label": "multi Flow", "disabled": false, "info": ""}, {"id": "mqtt_1749120698088_0_0", "type": "mqtt in", "z": "flow1", "name": "multi - temp", "topic": "temp", "qos": 2, "datatype": "auto-detect", "broker": "mqttBrokerConfig", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 160, "y": 120, "wires": [["processing_func_1749120698088_0_0"]]}, {"id": "processing_func_1749120698088_0_0", "type": "function", "z": "flow1", "name": "Processing temp", "func": "return msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 380, "y": 120, "wires": [["func_1749120698088_0_0"]]}, {"id": "debug_1749120698088_0_0", "type": "debug", "z": "flow1", "name": "Debug temp", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 820, "y": 120, "wires": []}, {"id": "func_1749120698088_0_0", "type": "function", "z": "flow1", "name": "Process temp", "func": "\nlet sensorMappingsJson = env.get(\"SENSOR_MAPPINGS\") || \"{}\";\nlet sensorMappings = {};\ntry {\n    sensorMappings = JSON.parse(sensorMappingsJson);\n} catch(e) {\n    node.error(\"Failed to parse SENSOR_MAPPINGS environment variable: \" + e.message);\n    sensorMappings = {};\n}\nlet incoming = msg.payload;\nlet topic = msg.topic; // This is the MQTT topic\nlet mapping = sensorMappings[topic] || {};\nlet fullLeshanUri = mapping.leshanUri || \"Unknown/0/5700\";\nmsg.payload = incoming || null;\nmsg.mqttTopic = topic; // Preserve the original MQTT topic\nmsg.topic = \"/\" + fullLeshanUri;\nmsg.deviceName = mapping.deviceName || topic;\nreturn msg;\n          ", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 580, "y": 120, "wires": [["debug_1749120698088_0_0", "lwm2m_client_out"]]}, {"id": "mqtt_1749120698088_1_0", "type": "mqtt in", "z": "flow1", "name": "multi - hum", "topic": "hum", "qos": 2, "datatype": "auto-detect", "broker": "mqttBrokerConfig", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 160, "y": 220, "wires": [["processing_func_1749120698088_1_0"]]}, {"id": "processing_func_1749120698088_1_0", "type": "function", "z": "flow1", "name": "Processing hum", "func": "return msg;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 380, "y": 220, "wires": [["func_1749120698088_1_0"]]}, {"id": "debug_1749120698088_1_0", "type": "debug", "z": "flow1", "name": "Debug hum", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "payload", "targetType": "msg", "statusVal": "", "statusType": "auto", "x": 820, "y": 220, "wires": []}, {"id": "func_1749120698088_1_0", "type": "function", "z": "flow1", "name": "Process hum", "func": "\nlet sensorMappingsJson = env.get(\"SENSOR_MAPPINGS\") || \"{}\";\nlet sensorMappings = {};\ntry {\n    sensorMappings = JSON.parse(sensorMappingsJson);\n} catch(e) {\n    node.error(\"Failed to parse SENSOR_MAPPINGS environment variable: \" + e.message);\n    sensorMappings = {};\n}\nlet incoming = msg.payload;\nlet topic = msg.topic; // This is the MQTT topic\nlet mapping = sensorMappings[topic] || {};\nlet fullLeshanUri = mapping.leshanUri || \"Unknown/0/5700\";\nmsg.payload = incoming || null;\nmsg.mqttTopic = topic; // Preserve the original MQTT topic\nmsg.topic = \"/\" + fullLeshanUri;\nmsg.deviceName = mapping.deviceName || topic;\nreturn msg;\n          ", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 580, "y": 220, "wires": [["debug_1749120698088_1_0", "lwm2m_client_out"]]}, {"id": "lwm2m_client_out", "type": "lwm2m client out", "z": "flow1", "name": "", "lwm2mClient": "lwm2m_client_in", "x": 750, "y": 160, "wires": []}, {"id": "0dc9c107d9fc9b76", "type": "lwm2m client in", "z": "flow1", "name": "", "lwm2mClient": "lwm2m_client_in", "subscribeObjectEvents": true, "outputAsObject": false, "x": 890, "y": 160, "wires": [["cf516c136c80ab05"]]}, {"id": "cf516c136c80ab05", "type": "function", "z": "flow1", "name": "function 6", "func": "const token = 'Pb3x9HQY-VOLWGaZAlrif5ThJDJ6oSpJvUSJtD62NY3EC5pXKDdpYmJer1X4yt1C5T_aYi8MgEcKByqP1rPjFQ==';\nconst org = 'ICT';\nconst bucket = 'olive';\nconst host = 'http://**********:8086';\n\nlet incoming = msg.payload;\nlet leshanUriFromMsg = incoming.uri;  // e.g. '/3303/0/5700' or '3303/0/5700'\n\n// Normalize incoming.uri by removing leading slash if any\nif (leshanUriFromMsg.startsWith('/')) {\n    leshanUriFromMsg = leshanUriFromMsg.slice(1);\n}\n\nlet sensorMappingJson = env.get('SENSOR_MAPPINGS') || '{}';\nlet sensorMapping = {};\n\ntry {\n    sensorMapping = JSON.parse(sensorMappingJson);\n} catch (e) {\n    node.error('Failed to parse SENSOR_MAPPINGS: ' + e.message);\n    sensorMapping = {};\n}\n\n// Reverse lookup: find the deviceName for the matching leshanUri\nlet sensorName = 'UnknownSensor';\nfor (const [topic, mapping] of Object.entries(sensorMapping)) {\n    if (mapping.leshanUri === leshanUriFromMsg) {\n        sensorName = mapping.deviceName;\n        break;\n    }\n}\n\nlet timestamp = incoming.ts || Date.now();\nlet value = parseFloat(incoming.value.value);\n\nconst fieldKey = sensorName.toLowerCase();\n\nlet tags = 'sensor=' + sensorName + ',uri=' + incoming.uri + ',eventType=' + incoming.eventType;\nmsg.url = host + '/api/v2/write?org=' + org + '&bucket=' + bucket + '&precision=ns';\nmsg.headers = {\n  'Authorization': 'Token ' + token,\n  'Content-Type': 'text/plain'\n};\n\nmsg.payload = 'olive,' + tags + ' ' + fieldKey + '=' + value + ' ' + (timestamp * 1e6);\nreturn msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 1020, "y": 160, "wires": [["551e6840d9ed962f", "f3a219ccdf105ec5"]]}, {"id": "551e6840d9ed962f", "type": "debug", "z": "flow1", "name": "debug 2", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1020, "y": 200, "wires": []}, {"id": "f3a219ccdf105ec5", "type": "http request", "z": "flow1", "name": "", "method": "POST", "ret": "txt", "paytoqs": "ignore", "url": "", "tls": "", "persist": false, "proxy": "", "insecureHTTPParser": false, "authType": "", "senderr": false, "headers": [{"keyType": "Content-Type", "keyValue": "", "valueType": "text/plain", "valueValue": ""}], "x": 1190, "y": 160, "wires": [["75c26e01925e8934"]]}, {"id": "75c26e01925e8934", "type": "debug", "z": "flow1", "name": "debug 3", "active": true, "tosidebar": true, "console": false, "tostatus": false, "complete": "true", "targetType": "full", "statusVal": "", "statusType": "auto", "x": 1200, "y": 200, "wires": []}, {"id": "5749d7f21812fd8c", "type": "comment", "z": "flow1", "name": "", "info": "temp==temperature\nhum==humidity", "x": 100, "y": 20, "wires": []}, {"id": "mqttBrokerConfig", "type": "mqtt-broker", "name": "", "broker": "160.97.28.155", "port": 1883, "clientid": "node-red-multi-1749120698088", "autoConnect": true, "usetls": false, "protocolVersion": "4", "keepalive": "60", "cleansession": true, "autoUnsubscribe": true, "birthTopic": "", "birthQos": "0", "birthRetain": "false", "birthPayload": "", "birthMsg": {}, "closeTopic": "", "closeQos": "0", "closeRetain": "false", "closePayload": "", "closeMsg": {}, "willTopic": "multi/status", "willQos": "0", "willRetain": "false", "willPayload": "offline", "willMsg": {}, "userProps": "", "sessionExpiry": ""}, {"id": "lwm2m_client_in", "type": "lwm2m client", "clientName": "multi", "enableDTLS": false, "clientPort": "56830", "lifetimeSec": "300", "reconnectSec": "60", "bootstrapIntervalSec": "3600", "maxRecvPacketSize": "16486", "requestBootstrap": false, "saveProvisionedConfig": false, "useIPv4": true, "serverHost": "**********", "serverPort": 5683, "redirectLwm2mClientLog": false, "dumpLwm2mMessages": false, "hideSensitiveInfo": true, "propagateInternalEvents": false, "objects": "{\"3303\":{\"0\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0},\"9999\":{\"type\":\"STRING\",\"acl\":\"R\",\"value\":\"temperature\"}}},\"3304\":{\"0\":{\"2\":true,\"5700\":{\"type\":\"FLOAT\",\"acl\":\"RW\",\"value\":0},\"9999\":{\"type\":\"STRING\",\"acl\":\"R\",\"value\":\"humidity\"}}}}"}]