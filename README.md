# Digital Twin Management Platform

A comprehensive platform for creating and managing digital twins of IoT devices with advanced AI agent integration, real-time data visualization, and intelligent monitoring capabilities.

## 🚀 Features

### **Core Platform**
- Create and manage virtual sensors and actuators
- Configure MQTT broker settings with dynamic templates
- Dynamically generate LwM2M objects with custom resources
- Deploy containers with Node-RED flows
- Real-time data persistence with InfluxDB integration

### **AI Agent Integration** ✨ *NEW*
- **Modern AG-UI Interface**: Professional chat-based AI assistant
- **Dual Mode Operation**: IoT data queries + General AI conversations
- **Multi-LLM Support**: Google Gemini 2.0 Flash & Anthropic Claude
- **Real-time Data Visualization**: Interactive charts and tables
- **Natural Language Queries**: Ask questions about sensor data in plain English
- **Streaming Responses**: Real-time AI responses with Server-Sent Events

### **Data Management**
- **InfluxDB Integration**: Time-series data storage and querying
- **MCP Protocol**: Model Context Protocol for standardized AI-data interaction
- **Advanced Visualization**: Recharts-powered interactive graphs
- **Data Standardization**: Automatic data type detection and formatting

## 🏗️ Project Structure

```
Digital-Twin-Project/
├── 📁 backend/                 # Node.js backend server
│   ├── 📁 mcp/                 # Model Context Protocol integration
│   │   ├── 📁 core/            # Core MCP functionality
│   │   ├── 📁 llm/             # LLM providers and services
│   │   ├── 📁 tools/           # Tool definitions and implementations
│   │   ├── 📁 enhanced/        # Advanced MCP features
│   │   └── 📁 config/          # Configuration management
│   ├── 📁 routes/              # API routes
│   │   └── agui.js             # AG-UI protocol endpoints
│   └── 📁 templates/           # Node-RED flow templates
├── 📁 frontend/                # React frontend application
│   ├── 📁 src/mcp-client/      # AI agent integration
│   │   ├── 📁 components/agui/ # Modern AG-UI components
│   │   ├── 📁 pages/           # AI agent pages
│   │   └── 📁 services/agui/   # AG-UI services
│   └── 📁 src/pages/           # Main application pages
├── 📁 node-red-custom/         # Custom Node-RED Docker image
```

## ⚙️ Setup

### Prerequisites

- **Node.js** (v18+ recommended)
- **Docker** (for containerized services)
- **InfluxDB** (for time-series data storage)
- **MQTT Broker** (e.g., HiveMQ Cloud)
- **LwM2M Server** (e.g., Leshan)

### Environment Variables

Create `.env` files in both `backend/` and `frontend/` directories:

#### Backend `.env`
```bash
# InfluxDB Configuration
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your_influxdb_token
INFLUXDB_ORG=ICT
INFLUXDB_BUCKET=test

# LLM API Keys
GOOGLE_API_KEY=your_google_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# Model Configuration
GOOGLE_MODEL=gemini-2.0-flash
ANTHROPIC_MODEL=claude-3-5-sonnet-20241022

# MCP Server
MCP_SERVER_PATH=/path/to/mcp/server
```

#### Frontend `.env`
```bash
# React App Configuration
REACT_APP_INFLUXDB_URL=http://localhost:8086
REACT_APP_INFLUXDB_ORG=ICT
REACT_APP_INFLUXDB_BUCKET=olive
```

### Installation

1. **Clone the repository:**
   ```bash
   git clone https://github.com/ShaheemNaqvi/Digital-Twin-Project.git
   cd Digital-Twin-Project
   ```

2. **Install dependencies:**
   ```bash
   # Backend
   cd backend && npm install

   # Frontend
   cd ../frontend && npm install --legacy-peer-deps
   ```

3. **Build the custom Node-RED image:**
   ```bash
   cd ../node-red-custom
   docker build -t custom-nodered-image -f Dockerfile .
   ```

4. **Start the services:**
   ```bash
   # Backend (Terminal 1)
   cd ../backend && npm start

   # Frontend (Terminal 2)
   cd ../frontend && npm run start-mcp
   ```

## 🎯 Usage

### **Access the Platform**
- **Main Application**: http://localhost:3000
- **AI Assistant**: http://localhost:3000/chats
- **Container Management**: http://localhost:3000/management
- **Template Management**: http://localhost:3000/templates

### **Core Platform Usage**
1. Navigate to the main application
2. Create a new virtual object (sensor or actuator)
3. Configure the MQTT broker settings
4. Add resources to the virtual object
5. Deploy the virtual object

### **AI Assistant Usage** ✨
1. **Access the AI Assistant**: Click "AI Assistant" in the navigation or visit `/chats`
2. **Select a Mode**: Choose between "MCP Mode" for data analysis and "LLM Mode" for general chat.
3. **Interact**: Start asking questions or giving commands.

## 🔧 API Endpoints

### **AG-UI Protocol Endpoints**
- `POST /api/agui/query` - Process IoT data queries with streaming responses
- `POST /api/agui/llm-chat` - Direct LLM conversations
- `GET /api/agui/status` - Get agent and system status
- `GET /api/agui/config` - Get agent configuration
- `GET /api/agui/tools` - Get available tools

### **Core Platform Endpoints**
- `GET /api/containers` - List containers
- `POST /api/containers` - Create container
- `GET /api/templates` - List templates
- `POST /api/templates` - Create template

## 🧪 Testing

### **Test the AI Assistant**
```bash
# Test IoT data query
curl -X POST http://localhost:5000/api/agui/query \
  -H "Content-Type: application/json" \
  -d '{
    "threadId": "test-thread",
    "runId": "test-run",
    "messages": [{"role": "user", "content": "What sensors are available?"}],
    "context": {"queryMode": "mcp"}
  }'

# Test direct LLM chat
curl -X POST http://localhost:5000/api/agui/llm-chat \
  -H "Content-Type: application/json" \
  -d '{
    "threadId": "test-thread",
    "runId": "test-run",
    "messages": [{"role": "user", "content": "Hello, how are you?"}]
  }'
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes
4. Commit your changes: `git commit -am 'Add some feature'`
5. Push to the branch: `git push origin feature-name`
6. Submit a pull request

## 📝 License

[MIT](LICENSE)

## 🙏 Acknowledgments

- **Model Context Protocol (MCP)** for standardized AI-data interaction
- **Google Gemini** and **Anthropic Claude** for LLM capabilities
- **InfluxDB** for time-series data management
- **React** and **Material-UI** for the frontend interface
