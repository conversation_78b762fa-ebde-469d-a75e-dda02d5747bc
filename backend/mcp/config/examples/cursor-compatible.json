{"mcpServers": {"influxdb": {"command": "npx", "args": ["influxdb-mcp-server"], "env": {"INFLUXDB_TOKEN": "your_influxdb_token_here", "INFLUXDB_URL": "http://localhost:8086", "INFLUXDB_ORG": "your_org_name"}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {"UPSTASH_REDIS_REST_URL": "your_redis_url", "UPSTASH_REDIS_REST_TOKEN": "your_redis_token"}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem@latest"], "env": {"FILESYSTEM_ALLOWED_DIRECTORIES": "/home/<USER>/projects,/tmp"}}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search@latest"], "env": {"BRAVE_API_KEY": "your_brave_api_key_here"}}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github@latest"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "your_github_token_here"}}, "sqlite": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite@latest"], "env": {"SQLITE_DATABASE_PATH": "/path/to/your/database.db"}}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer@latest"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory@latest"]}, "fetch": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-fetch@latest"]}, "everything": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-everything@latest"], "env": {"EVERYTHING_PATH": "/usr/local/bin/es"}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking@latest"]}}}