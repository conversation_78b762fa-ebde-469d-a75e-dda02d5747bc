/**
 * MCP Query Component
 * Allows users to send natural language queries that use MCP tools
 */

import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Chip,
  Paper
} from '@mui/material';
import { Send as SendIcon, Psychology as AIIcon } from '@mui/icons-material';
import AGUIAgent from '../../services/agui/AGUIAgent';

const MCPQueryComponent = ({ tools = [] }) => {
  const [query, setQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState(null);
  const [error, setError] = useState(null);

  const agent = new AGUIAgent();

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!query.trim()) return;

    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      const messages = [
        {
          role: 'user',
          content: query
        }
      ];

      const observable = agent.runAgent({
        messages,
        tools,
        context: {
          queryMode: 'mcp',
          capabilities: ['filesystem-access', 'database-access']
        }
      });

      let fullResponse = '';
      let toolCalls = [];

      observable.subscribe({
        next: (event) => {
          console.log('MCP Event:', event);
          
          switch (event.type) {
            case 'TEXT_MESSAGE_CONTENT':
              fullResponse += event.content || '';
              break;
            case 'TOOL_CALL_START':
              toolCalls.push({
                name: event.toolName,
                args: null,
                result: null
              });
              break;
            case 'TOOL_CALL_ARGS':
              if (toolCalls.length > 0) {
                toolCalls[toolCalls.length - 1].args = event.args;
              }
              break;
            case 'TOOL_CALL_END':
              if (toolCalls.length > 0) {
                toolCalls[toolCalls.length - 1].result = event.result;
              }
              break;
            case 'RUN_FINISHED':
              setResponse({
                content: fullResponse,
                toolCalls: toolCalls
              });
              setLoading(false);
              break;
            case 'RUN_ERROR':
              setError(event.message);
              setLoading(false);
              break;
          }
        },
        error: (err) => {
          setError(err.message);
          setLoading(false);
        }
      });

    } catch (err) {
      setError(err.message);
      setLoading(false);
    }
  };

  const getSuggestedQueries = () => {
    return [
      "Show me temperature data from the last hour",
      "List files in the projects directory",
      "Read the config.json file",
      "Write a test file with current timestamp",
      "Query all sensor data from today",
      "Create a new directory called 'test-folder'"
    ];
  };

  return (
    <Box>
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            <AIIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Ask AI to Use MCP Tools
          </Typography>
          
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Ask questions in natural language. The AI will automatically use the appropriate MCP tools.
          </Typography>

          <form onSubmit={handleSubmit}>
            <TextField
              fullWidth
              multiline
              rows={3}
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Ask me anything about your data or files..."
              variant="outlined"
              sx={{ mb: 2 }}
              disabled={loading}
            />
            
            <Button
              type="submit"
              variant="contained"
              startIcon={loading ? <CircularProgress size={16} /> : <SendIcon />}
              disabled={loading || !query.trim()}
              fullWidth
            >
              {loading ? 'Processing...' : 'Send Query'}
            </Button>
          </form>

          {/* Suggested Queries */}
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Try these examples:
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {getSuggestedQueries().map((suggestion, index) => (
                <Chip
                  key={index}
                  label={suggestion}
                  variant="outlined"
                  size="small"
                  onClick={() => setQuery(suggestion)}
                  sx={{ cursor: 'pointer' }}
                />
              ))}
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}

      {/* Response Display */}
      {response && (
        <Card sx={{ mt: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              AI Response
            </Typography>
            
            {/* Tool Calls */}
            {response.toolCalls && response.toolCalls.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Tools Used:
                </Typography>
                {response.toolCalls.map((toolCall, index) => (
                  <Paper key={index} sx={{ p: 2, mb: 1, bgcolor: 'grey.50' }}>
                    <Typography variant="body2" fontWeight="bold">
                      {toolCall.name}
                    </Typography>
                    {toolCall.args && (
                      <Typography variant="body2" color="text.secondary" component="pre" sx={{ fontSize: '0.8rem', mt: 1 }}>
                        Args: {JSON.stringify(toolCall.args, null, 2)}
                      </Typography>
                    )}
                    {toolCall.result && (
                      <Typography variant="body2" color="text.secondary" component="pre" sx={{ fontSize: '0.8rem', mt: 1 }}>
                        Result: {JSON.stringify(toolCall.result, null, 2)}
                      </Typography>
                    )}
                  </Paper>
                ))}
              </Box>
            )}

            {/* AI Response Content */}
            <Paper sx={{ p: 2, bgcolor: 'primary.50' }}>
              <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                {response.content}
              </Typography>
            </Paper>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default MCPQueryComponent;
