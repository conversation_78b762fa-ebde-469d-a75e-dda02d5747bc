{"mcpServers": {"influxdb": {"command": "npx", "args": ["influxdb-mcp-server"], "env": {"INFLUXDB_TOKEN": "your_token_here", "INFLUXDB_URL": "http://localhost:8086", "INFLUXDB_ORG": "your_org"}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {"UPSTASH_REDIS_REST_URL": "your_redis_url", "UPSTASH_REDIS_REST_TOKEN": "your_redis_token"}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem@latest"], "env": {"FILESYSTEM_ALLOWED_DIRECTORIES": "/home/<USER>/projects,/tmp"}}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search@latest"], "env": {"BRAVE_API_KEY": "your_brave_api_key"}}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres@latest"], "env": {"POSTGRES_CONNECTION_STRING": "postgresql://user:password@localhost:5432/dbname"}}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github@latest"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "your_github_token"}}}}