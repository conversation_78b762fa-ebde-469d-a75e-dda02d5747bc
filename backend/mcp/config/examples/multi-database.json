{"globalSettings": {"loadBalancing": "priority", "failover": true, "healthMonitoring": true, "logging": {"level": "info", "enableMetrics": true}}, "servers": [{"id": "influxdb-production", "name": "InfluxDB Production Server", "description": "Primary production InfluxDB instance for real-time IoT data", "enabled": true, "connection": {"type": "stdio", "command": "node", "args": ["${MCP_INFLUXDB_SERVER_PATH}"], "timeout": 30000, "retryAttempts": 3, "retryDelay": 1000}, "authentication": {"type": "none", "envVars": [{"name": "INFLUXDB_TOKEN", "fromEnv": "INFLUXDB_PROD_TOKEN"}, {"name": "INFLUXDB_ORG", "fromEnv": "INFLUXDB_ORG"}, {"name": "INFLUXDB_URL", "fromEnv": "INFLUXDB_PROD_URL"}]}, "capabilities": ["time-series", "data-query", "data-write", "analytics", "iot-data", "real-time"], "tools": [{"name": "query-data", "description": "Query time-series data from production InfluxDB", "category": "data-access"}, {"name": "write-data", "description": "Write data to production InfluxDB", "category": "data-write"}, {"name": "create-bucket", "description": "Create a new InfluxDB bucket", "category": "management"}], "metadata": {"version": "1.0.0", "priority": 10, "tags": ["database", "time-series", "iot", "production", "primary"], "maintainer": "DevOps Team", "documentation": "https://docs.influxdata.com/"}, "healthCheck": {"enabled": true, "interval": 30000, "timeout": 5000, "method": "listTools"}}, {"id": "influxdb-staging", "name": "InfluxDB Staging Server", "description": "Staging InfluxDB instance for testing and development", "enabled": true, "connection": {"type": "stdio", "command": "node", "args": ["${MCP_INFLUXDB_SERVER_PATH}"], "timeout": 30000, "retryAttempts": 2, "retryDelay": 2000}, "authentication": {"type": "none", "envVars": [{"name": "INFLUXDB_TOKEN", "fromEnv": "INFLUXDB_STAGING_TOKEN"}, {"name": "INFLUXDB_ORG", "fromEnv": "INFLUXDB_ORG"}, {"name": "INFLUXDB_URL", "fromEnv": "INFLUXDB_STAGING_URL"}]}, "capabilities": ["time-series", "data-query", "data-write", "testing", "development"], "tools": [{"name": "query-data", "description": "Query time-series data from staging InfluxDB", "category": "data-access"}, {"name": "write-data", "description": "Write test data to staging InfluxDB", "category": "data-write"}], "metadata": {"version": "1.0.0", "priority": 7, "tags": ["database", "time-series", "staging", "testing"], "maintainer": "Development Team"}, "healthCheck": {"enabled": true, "interval": 60000, "timeout": 10000, "method": "listTools"}}, {"id": "postgresql-analytics", "name": "PostgreSQL Analytics Server", "description": "PostgreSQL server for complex analytics and reporting", "enabled": false, "connection": {"type": "stdio", "command": "node", "args": ["${MCP_POSTGRES_SERVER_PATH}"], "timeout": 45000, "retryAttempts": 3, "retryDelay": 1500}, "authentication": {"type": "none", "envVars": [{"name": "POSTGRES_HOST", "fromEnv": "POSTGRES_HOST"}, {"name": "POSTGRES_PORT", "fromEnv": "POSTGRES_PORT"}, {"name": "POSTGRES_DB", "fromEnv": "POSTGRES_DB"}, {"name": "POSTGRES_USER", "fromEnv": "POSTGRES_USER"}, {"name": "POSTGRES_PASSWORD", "fromEnv": "POSTGRES_PASSWORD"}]}, "capabilities": ["relational", "analytics", "reporting", "complex-queries", "aggregation"], "tools": [{"name": "execute-sql", "description": "Execute SQL queries on PostgreSQL", "category": "data-access"}, {"name": "generate-report", "description": "Generate analytics reports", "category": "analytics"}], "metadata": {"version": "1.0.0", "priority": 5, "tags": ["database", "relational", "analytics", "reporting"], "maintainer": "Analytics Team"}, "healthCheck": {"enabled": true, "interval": 120000, "timeout": 15000, "method": "listTools"}}]}