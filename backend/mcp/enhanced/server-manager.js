/**
 * Enhanced MCP Server Manager
 * JSON-configuration-based dynamic MCP server management
 * Supports multiple connection types, health monitoring, load balancing, and failover
 */

const { Client } = require("@modelcontextprotocol/sdk/client/index.js");
const { StdioClientTransport } = require("@modelcontextprotocol/sdk/client/stdio.js");
const EventEmitter = require('events');
const MCPConfigLoader = require('../config/config-loader');
const fs = require('fs');
const path = require('path');

class MCPServerManager extends EventEmitter {
  constructor(configPath = null) {
    super();
    this.servers = new Map();
    this.activeServers = new Set();
    this.healthCheckInterval = null;
    this.loadBalancingStrategy = 'priority';
    this.currentServerIndex = 0;
    this.configLoader = new MCPConfigLoader();
    this.config = null;
    this.configPath = configPath;
    this.isInitialized = false;
  }

  /**
   * Initialize the server manager with configuration
   * @param {string} configPath - Optional path to configuration file
   * @returns {Promise<boolean>} Whether initialization was successful
   */
  async initialize(configPath = null) {
    try {
      // Load configuration
      this.configPath = configPath || this.configPath;
      this.config = this.configPath
        ? this.configLoader.loadConfig(this.configPath)
        : this.configLoader.loadDefaultConfig();

      // Apply global settings
      this.loadBalancingStrategy = this.config.globalSettings.loadBalancing;

      console.log(`Loaded MCP configuration with ${this.config.servers.length} servers`);

      // Register all enabled servers
      const registrationPromises = this.config.servers
        .filter(server => server.enabled)
        .map(server => this.registerServerFromConfig(server));

      const results = await Promise.allSettled(registrationPromises);

      // Log results
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;

      console.log(`Server registration complete: ${successful} successful, ${failed} failed`);

      // Start health monitoring if enabled
      if (this.config.globalSettings.healthMonitoring) {
        this.startHealthMonitoring();
      }

      this.isInitialized = true;
      this.emit('initialized', { successful, failed, total: this.config.servers.length });

      return successful > 0;
    } catch (error) {
      console.error('Failed to initialize MCP Server Manager:', error);
      this.emit('initializationFailed', error);
      return false;
    }
  }

  /**
   * Register a server from configuration object
   * @param {Object} serverConfig - Server configuration from JSON
   * @returns {Promise<Object>} Server info
   */
  async registerServerFromConfig(serverConfig) {
    const { id, name, connection, authentication, capabilities, metadata, healthCheck } = serverConfig;

    if (this.servers.has(id)) {
      throw new Error(`Server with ID '${id}' is already registered`);
    }

    const serverInfo = {
      id,
      name,
      description: serverConfig.description || '',
      connection,
      authentication,
      capabilities: capabilities || [],
      metadata: {
        ...metadata,
        registeredAt: new Date(),
        lastConnectedAt: null,
        totalQueries: 0,
        successfulQueries: 0,
        failedQueries: 0
      },
      healthCheck,
      client: null,
      transport: null,
      isConnected: false,
      lastHealthCheck: null,
      connectionAttempts: 0,
      maxConnectionAttempts: connection.retryAttempts || 3,
      tools: [],
      expectedTools: serverConfig.tools || []
    };

    this.servers.set(id, serverInfo);
    console.log(`Registered MCP server: ${name} (${id})`);

    // Attempt to connect
    await this.connectServer(id);

    return serverInfo;
  }

  /**
   * Register a new MCP server (legacy method for backward compatibility)
   * @param {Object} config - Server configuration
   * @deprecated Use registerServerFromConfig or initialize with JSON config
   */
  async registerServer(config) {
    console.warn('registerServer is deprecated. Use registerServerFromConfig or JSON configuration.');

    // Convert legacy config to new format
    const serverConfig = {
      id: config.id,
      name: config.name,
      description: config.description || '',
      enabled: true,
      connection: {
        type: 'stdio',
        command: config.command,
        args: config.args || [],
        timeout: 30000,
        retryAttempts: config.maxConnectionAttempts || 3,
        retryDelay: 1000
      },
      authentication: {
        type: 'none',
        envVars: Object.entries(config.env || {}).map(([name, value]) => ({ name, value }))
      },
      capabilities: config.capabilities || [],
      metadata: {
        priority: config.priority || 1,
        tags: [],
        maintainer: 'Legacy',
        version: '1.0.0'
      },
      healthCheck: {
        enabled: true,
        interval: config.healthCheckInterval || 30000,
        timeout: 5000,
        method: 'listTools'
      }
    };

    return this.registerServerFromConfig(serverConfig);
  }

  /**
   * Connect to a specific server
   * @param {string} serverId - Server ID
   */
  async connectServer(serverId) {
    const server = this.servers.get(serverId);
    if (!server) {
      throw new Error(`Server '${serverId}' not found`);
    }

    if (server.isConnected) {
      console.log(`Server '${serverId}' is already connected`);
      return true;
    }

    try {
      console.log(`Connecting to MCP server: ${server.name} (${server.connection.type})`);

      // Create client
      server.client = new Client({
        name: `Enhanced MCP Client - ${server.name}`,
        version: "1.0.0"
      });

      // Create transport based on connection type
      server.transport = await this.createTransport(server);

      // Set up error handler
      server.client.onerror = (error) => {
        console.error(`MCP server '${serverId}' error:`, error);
        this.handleServerError(serverId, error);
      };

      // Connect with timeout
      const connectPromise = server.client.connect(server.transport);
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Connection timeout')), server.connection.timeout || 30000)
      );

      await Promise.race([connectPromise, timeoutPromise]);

      // Get available tools
      const toolsResponse = await server.client.listTools();
      server.tools = toolsResponse.tools || [];

      // Validate expected tools
      this.validateServerTools(server);

      // Update server status
      server.isConnected = true;
      server.connectionAttempts = 0;
      server.metadata.lastConnectedAt = new Date();
      this.activeServers.add(serverId);

      console.log(`Successfully connected to MCP server: ${server.name}`);
      console.log(`Available tools: ${server.tools.map(t => t.name).join(', ')}`);

      this.emit('serverConnected', { serverId, server });
      return true;

    } catch (error) {
      console.error(`Failed to connect to MCP server '${serverId}':`, error);
      server.connectionAttempts++;
      server.isConnected = false;
      this.activeServers.delete(serverId);

      this.emit('serverConnectionFailed', { serverId, server, error });

      if (server.connectionAttempts >= server.maxConnectionAttempts) {
        console.error(`Max connection attempts reached for server '${serverId}'`);
        this.emit('serverMaxAttemptsReached', { serverId, server });
      }

      return false;
    }
  }

  /**
   * Create transport based on connection configuration
   * @param {Object} server - Server configuration
   * @returns {Promise<Object>} Transport instance
   */
  async createTransport(server) {
    const { connection, authentication } = server;

    switch (connection.type) {
      case 'stdio':
        return this.createStdioTransport(server);

      case 'http':
      case 'websocket':
      case 'tcp':
        throw new Error(`Transport type '${connection.type}' not yet implemented`);

      default:
        throw new Error(`Unknown transport type: ${connection.type}`);
    }
  }

  /**
   * Create STDIO transport
   * @param {Object} server - Server configuration
   * @returns {Object} STDIO transport
   */
  createStdioTransport(server) {
    const { connection, authentication } = server;

    // Prepare environment variables
    const env = { ...process.env };

    if (authentication?.processedEnvVars) {
      Object.assign(env, authentication.processedEnvVars);
    }

    return new StdioClientTransport({
      command: connection.command,
      args: connection.args || [],
      env
    });
  }

  /**
   * Validate that server provides expected tools
   * @param {Object} server - Server info
   */
  validateServerTools(server) {
    if (!server.expectedTools || server.expectedTools.length === 0) {
      return; // No validation needed
    }

    const availableToolNames = server.tools.map(t => t.name);
    const missingTools = server.expectedTools
      .map(t => t.name)
      .filter(name => !availableToolNames.includes(name));

    if (missingTools.length > 0) {
      console.warn(`Server '${server.id}' is missing expected tools: ${missingTools.join(', ')}`);
      this.emit('serverToolsMismatch', { serverId: server.id, missingTools, server });
    }
  }

  /**
   * Disconnect from a specific server
   * @param {string} serverId - Server ID
   */
  async disconnectServer(serverId) {
    const server = this.servers.get(serverId);
    if (!server) {
      throw new Error(`Server '${serverId}' not found`);
    }

    if (server.client && server.isConnected) {
      try {
        await server.client.close();
      } catch (error) {
        console.error(`Error closing server '${serverId}':`, error);
      }
    }

    server.isConnected = false;
    server.client = null;
    server.transport = null;
    this.activeServers.delete(serverId);

    console.log(`Disconnected from MCP server: ${server.name}`);
    this.emit('serverDisconnected', { serverId, server });
  }

  /**
   * Get the best available server for a query
   * @param {Array} requiredCapabilities - Required server capabilities
   * @returns {Object|null} Server info or null if none available
   */
  getAvailableServer(requiredCapabilities = []) {
    const availableServers = Array.from(this.activeServers)
      .map(id => this.servers.get(id))
      .filter(server => {
        if (!server.isConnected) return false;
        
        // Check if server has required capabilities
        if (requiredCapabilities.length > 0) {
          return requiredCapabilities.every(cap => 
            server.capabilities.includes(cap)
          );
        }
        
        return true;
      })
      .sort((a, b) => b.priority - a.priority); // Sort by priority (descending)

    if (availableServers.length === 0) {
      return null;
    }

    // Load balancing strategy
    switch (this.loadBalancingStrategy) {
      case 'round-robin':
        const server = availableServers[this.currentServerIndex % availableServers.length];
        this.currentServerIndex++;
        return server;
      
      case 'priority':
        return availableServers[0]; // Highest priority
      
      case 'least-loaded':
        return availableServers.reduce((least, current) => 
          current.metadata.totalQueries < least.metadata.totalQueries ? current : least
        );
      
      default:
        return availableServers[0];
    }
  }

  /**
   * Call a tool on the best available server
   * @param {string} toolName - Tool name
   * @param {Object} params - Tool parameters
   * @param {Array} requiredCapabilities - Required server capabilities
   * @returns {Promise<Object>} Tool result
   */
  async callTool(toolName, params, requiredCapabilities = []) {
    // Find server that has the specific tool
    let server = null;
    let tool = null;

    for (const serverId of this.activeServers) {
      const candidateServer = this.servers.get(serverId);
      if (!candidateServer || !candidateServer.isConnected) continue;

      const candidateTool = candidateServer.tools.find(t => t.name === toolName);
      if (candidateTool) {
        // Check if server meets required capabilities
        if (requiredCapabilities.length === 0 ||
            requiredCapabilities.every(cap => candidateServer.capabilities.includes(cap))) {
          server = candidateServer;
          tool = candidateTool;
          break;
        }
      }
    }

    if (!server) {
      throw new Error(`Tool '${toolName}' not available on any connected server`);
    }

    try {
      server.metadata.totalQueries++;
      
      const result = await server.client.callTool({
        name: toolName,
        arguments: params
      });

      server.metadata.successfulQueries++;
      
      return {
        result,
        serverId: server.id,
        serverName: server.name,
        metadata: {
          toolName,
          executionTime: Date.now(),
          serverId: server.id
        }
      };

    } catch (error) {
      server.metadata.failedQueries++;
      console.error(`Tool call failed on server '${server.id}':`, error);
      
      // Try failover to another server
      const fallbackServer = this.getAvailableServer(requiredCapabilities);
      if (fallbackServer && fallbackServer.id !== server.id) {
        console.log(`Attempting failover to server '${fallbackServer.id}'`);
        return this.callTool(toolName, params, requiredCapabilities);
      }
      
      throw error;
    }
  }

  /**
   * Get all available tools from all connected servers
   * @returns {Array} Combined list of tools with server information
   */
  getAllTools() {
    const allTools = [];
    
    for (const [serverId, server] of this.servers) {
      if (server.isConnected && server.tools) {
        server.tools.forEach(tool => {
          allTools.push({
            ...tool,
            serverId,
            serverName: server.name,
            serverCapabilities: server.capabilities
          });
        });
      }
    }
    
    return allTools;
  }

  /**
   * Get server statistics
   * @returns {Object} Server statistics
   */
  getServerStats() {
    const stats = {
      totalServers: this.servers.size,
      activeServers: this.activeServers.size,
      inactiveServers: this.servers.size - this.activeServers.size,
      totalTools: this.getAllTools().length,
      servers: {}
    };

    for (const [serverId, server] of this.servers) {
      stats.servers[serverId] = {
        id: server.id,
        name: server.name,
        isConnected: server.isConnected,
        capabilities: server.capabilities,
        toolCount: server.tools.length,
        metadata: server.metadata
      };
    }

    return stats;
  }

  /**
   * Start health monitoring for all servers
   */
  startHealthMonitoring() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.healthCheckInterval = setInterval(async () => {
      for (const [serverId, server] of this.servers) {
        if (server.isConnected) {
          try {
            // Simple health check - list tools
            await server.client.listTools();
            server.lastHealthCheck = new Date();
          } catch (error) {
            console.warn(`Health check failed for server '${serverId}':`, error);
            this.handleServerError(serverId, error);
          }
        } else if (server.connectionAttempts < server.maxConnectionAttempts) {
          // Try to reconnect
          console.log(`Attempting to reconnect to server '${serverId}'`);
          await this.connectServer(serverId);
        }
      }
    }, 30000); // Check every 30 seconds

    console.log('Started health monitoring for MCP servers');
  }

  /**
   * Stop health monitoring
   */
  stopHealthMonitoring() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      console.log('Stopped health monitoring for MCP servers');
    }
  }

  /**
   * Handle server errors
   * @param {string} serverId - Server ID
   * @param {Error} error - Error object
   */
  handleServerError(serverId, error) {
    const server = this.servers.get(serverId);
    if (server) {
      server.isConnected = false;
      this.activeServers.delete(serverId);
      this.emit('serverError', { serverId, server, error });
    }
  }

  /**
   * Add a new server from JSON configuration
   * @param {Object} serverConfig - Server configuration object
   * @returns {Promise<Object>} Server info
   */
  async addServer(serverConfig) {
    // Validate configuration
    if (!this.configLoader.validateServerConfig(serverConfig)) {
      throw new Error('Invalid server configuration');
    }

    // Process the configuration
    const processedConfig = this.configLoader.processServerConfig(serverConfig);

    // Register and connect
    return this.registerServerFromConfig(processedConfig);
  }

  /**
   * Remove a server
   * @param {string} serverId - Server ID
   * @returns {Promise<boolean>} Whether removal was successful
   */
  async removeServer(serverId) {
    if (!this.servers.has(serverId)) {
      throw new Error(`Server '${serverId}' not found`);
    }

    // Disconnect first
    await this.disconnectServer(serverId);

    // Remove from maps
    this.servers.delete(serverId);
    this.activeServers.delete(serverId);

    console.log(`Removed server: ${serverId}`);
    this.emit('serverRemoved', { serverId });

    return true;
  }

  /**
   * Reload configuration from file
   * @param {string} configPath - Optional new config path
   * @returns {Promise<boolean>} Whether reload was successful
   */
  async reloadConfiguration(configPath = null) {
    try {
      console.log('Reloading MCP server configuration...');

      // Shutdown existing servers
      await this.shutdown();

      // Reinitialize with new config
      return this.initialize(configPath || this.configPath);

    } catch (error) {
      console.error('Failed to reload configuration:', error);
      this.emit('configurationReloadFailed', error);
      return false;
    }
  }

  /**
   * Get current configuration
   * @returns {Object} Current configuration
   */
  getConfiguration() {
    return this.config;
  }

  /**
   * Update global settings
   * @param {Object} settings - New global settings
   */
  updateGlobalSettings(settings) {
    if (this.config) {
      this.config.globalSettings = { ...this.config.globalSettings, ...settings };
      this.loadBalancingStrategy = this.config.globalSettings.loadBalancing;

      this.emit('globalSettingsUpdated', this.config.globalSettings);
    }
  }

  /**
   * Shutdown all servers
   */
  async shutdown() {
    console.log('Shutting down MCP Server Manager...');

    this.stopHealthMonitoring();

    const disconnectPromises = Array.from(this.servers.keys()).map(serverId =>
      this.disconnectServer(serverId).catch(error =>
        console.error(`Error disconnecting server '${serverId}':`, error)
      )
    );

    await Promise.all(disconnectPromises);

    this.servers.clear();
    this.activeServers.clear();
    this.isInitialized = false;

    console.log('MCP Server Manager shutdown complete');
    this.emit('shutdown');
  }
}

module.exports = MCPServerManager;
