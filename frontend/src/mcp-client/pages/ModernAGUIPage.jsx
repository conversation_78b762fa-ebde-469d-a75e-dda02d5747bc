/**
 * Modern AG-UI Page - Full-screen professional interface
 * Standalone page for the modern agent UI experience
 */

import React, { useState, useCallback, useMemo } from 'react';
import { Box, CssBaseline } from '@mui/material';
import ModernAGUIChatInterface from '../components/agui/ModernAGUIChatInterface';

const API_HOST = process.env.REACT_APP_API_HOST || 'localhost';
const API_BASE_URL = `http://${API_HOST}:5000`;

/**
 * Modern AG-UI Page Component - Full Screen
 */
const ModernAGUIPage = () => {
  const [agentConfig] = useState({
    agentId: 'modern-digital-twin-assistant',
    description: 'Modern Digital Twin Assistant with Professional UI',
    baseUrl: API_BASE_URL,
    initialState: {
      protocolVersion: '1.0',
      capabilities: ['sensor-data', 'real-time-monitoring', 'data-visualization', 'general-ai'],
      connectionStatus: 'connected'
    }
  });

  // Handle state changes
  const handleStateChange = useCallback((state) => {
    console.log('[Modern AG-UI] Agent state changed:', state);
  }, []);

  // Handle new messages
  const handleMessageAdd = useCallback((message) => {
    console.log('[Modern AG-UI] New message:', message);
  }, []);

  // Memoize tools and context to prevent re-initialization
  const tools = useMemo(() => [
    { name: 'query-data', description: 'Query sensor data from InfluxDB' },
    { name: 'get-status', description: 'Get system status' },
    { name: 'analyze-trends', description: 'Analyze data trends' }
  ], []);

  const context = useMemo(() => ({
    protocolVersion: '1.0',
    environment: 'production',
    capabilities: ['streaming', 'tool-calls', 'state-management']
  }), []);

  return (
    <>
      <CssBaseline />
      <Box sx={{
        height: '100vh',
        width: '100vw',
        overflow: 'hidden',
        bgcolor: '#F7FAFC' // Your app's background color
      }}>
        <ModernAGUIChatInterface
          agentConfig={agentConfig}
          onStateChange={handleStateChange}
          onMessageAdd={handleMessageAdd}
          tools={tools}
          context={context}
        />
      </Box>
    </>
  );
};

export default ModernAGUIPage;
