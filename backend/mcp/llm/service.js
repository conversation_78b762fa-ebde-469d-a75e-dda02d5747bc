/**
 * Enhanced LLM Service Layer for orchestration logic
 * Handles LLM query processing, function/tool calls, and response formatting
 * Integrates with enhanced MCP Server Manager and Response Standardizer
 */

const mcpClient = require('../core/client');
const GoogleAdapter = require('./providers/google');
const AnthropicAdapter = require('./providers/anthropic');
const getSystemPrompt = require('./prompts/iot-system');
const getGeneralSystemPrompt = require('./prompts/general-system');
const MCPServerManager = require('../enhanced/server-manager');
const ResponseStandardizer = require('../enhanced/response-standardizer');

// Get configuration from environment variables
const INFLUXDB_ORG = process.env.INFLUXDB_ORG || 'ICT';

const GOOGLE_MODEL = process.env.GOOGLE_MODEL || 'gemini-2.0-flash';
const ANTHROPIC_MODEL = process.env.ANTHROPIC_MODEL || 'claude-3-5-sonnet-20241022';

// LLM client initialization (moved from legacy)
let googleAI = null;
if (process.env.GOOGLE_API_KEY) {
  try {
    const { GoogleGenerativeAI } = require('@google/generative-ai');
    googleAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
  } catch (error) {
    console.error('Error initializing Google Gemini client:', error.message);
  }
}

let anthropic = null;
if (process.env.ANTHROPIC_API_KEY) {
  try {
    const { Anthropic } = require('@anthropic-ai/sdk');
    anthropic = new Anthropic({ apiKey: process.env.ANTHROPIC_API_KEY });
  } catch (error) {
    console.error('Error initializing Anthropic client:', error.message);
  }
}

// Provider registry
const providers = {
  google: new GoogleAdapter(googleAI, GOOGLE_MODEL, getSystemPrompt),
  anthropic: new AnthropicAdapter(anthropic, ANTHROPIC_MODEL, getSystemPrompt),
};

// Module-level variables
let activeLLM = 'google'; // Default to google
let mcpServerManager = null;
let responseStandardizer = null;

// Initialize enhanced components
function initializeEnhancedComponents() {
  if (!mcpServerManager) {
    mcpServerManager = new MCPServerManager();

    // Register default InfluxDB MCP server
    const influxConfig = {
      id: 'influxdb-primary',
      name: 'InfluxDB Primary Server',
      command: 'node',
      args: [process.env.MCP_SERVER_PATH],
      env: {
        INFLUXDB_TOKEN: process.env.INFLUXDB_TOKEN,
        INFLUXDB_ORG: INFLUXDB_ORG,
        INFLUXDB_URL: process.env.INFLUXDB_URL || 'http://localhost:8086'
      },
      capabilities: ['query-data', 'time-series', 'influxdb'],
      priority: 10
    };

    mcpServerManager.registerServer(influxConfig).catch(error => {
      console.error('Failed to register InfluxDB MCP server:', error);
    });

    // Start health monitoring
    mcpServerManager.startHealthMonitoring();
  }

  if (!responseStandardizer) {
    responseStandardizer = new ResponseStandardizer();
  }
}

function getActiveLLM() {
  return activeLLM;
}

function setActiveLLM(llm) {
  if (llm !== 'google' && llm !== 'anthropic') {
    console.error(`Invalid LLM: ${llm}. Must be 'google' or 'anthropic'`);
    return false;
  }
  if (llm === 'google' && !process.env.GOOGLE_API_KEY) {
    console.error('Google API key is not set');
    return false;
  }
  if (llm === 'anthropic' && !process.env.ANTHROPIC_API_KEY) {
    console.error('Anthropic API key is not set');
    return false;
  }
  activeLLM = llm;
  return true;
}

function getActiveProvider() {
  return providers[activeLLM];
}

/**
 * Process a query using the enhanced system with multiple MCP servers
 * @param {string} query - Natural language query
 * @returns {Promise<Object>} Processed and standardized response
 */
async function processQuery(query) {
  try {
    // Initialize enhanced components if not already done
    initializeEnhancedComponents();

    // Check if we have any available MCP servers
    if (!mcpServerManager || mcpServerManager.activeServers.size === 0) {
      // Fallback to legacy client if no enhanced servers available
      if (!mcpClient.isConnected) {
        throw new Error('No MCP servers available');
      }
      return await processQueryLegacy(query);
    }

    const adapter = getActiveProvider();
    if (!adapter) {
      throw new Error('No active LLM adapter is available');
    }

    // Create enhanced tool client that uses the server manager
    const enhancedToolClient = {
      getTools: () => mcpServerManager.getAllTools(),
      callTool: async (toolName, params) => {
        console.log(`[DEBUG] Tool call requested: ${toolName} with params:`, params);
        const result = await mcpServerManager.callTool(toolName, params);
        console.log(`[DEBUG] Tool call result for ${toolName}:`, JSON.stringify(result, null, 2));
        return result.result;
      }
    };

    // Debug: Log available tools
    const availableTools = enhancedToolClient.getTools();
    console.log('[DEBUG] Available tools for LLM:', availableTools.map(t => t.name));

    // Debug: Log incoming query
    console.log('[DEBUG] Incoming MCP query:', query);

    // Process query with enhanced system
    const llmResult = await adapter.processQuery(query, enhancedToolClient);
    console.log('[DEBUG] Raw LLM adapter response:', JSON.stringify(llmResult, null, 2));

    // Standardize the response
    const standardizedResponse = responseStandardizer.standardizeResponse(
      llmResult.data || llmResult.content,
      {
        query,
        llmProvider: activeLLM,
        toolResults: llmResult.toolResults,
        processingTimestamp: new Date().toISOString()
      }
    );

    // Merge LLM response with standardized data
    return {
      type: standardizedResponse.success ? standardizedResponse.type : 'error',
      content: llmResult.content,
      data: standardizedResponse.data,
      toolResults: llmResult.toolResults || [],
      metadata: {
        ...standardizedResponse.metadata,
        llmProvider: activeLLM,
        serverStats: mcpServerManager.getServerStats()
      }
    };

  } catch (error) {
    console.error('Enhanced query processing error:', error);
    return {
      type: 'error',
      content: `Error processing query: ${error.message}`,
      data: null,
      metadata: {
        error: true,
        errorType: error.name,
        timestamp: new Date().toISOString()
      }
    };
  }
}

/**
 * Process a direct query to LLM without MCP tools (for general conversation)
 * @param {string} query - Natural language query
 * @returns {Promise<Object>} Direct LLM response
 */
async function processDirectQuery(query) {
  try {
    const adapter = getActiveProvider();
    if (!adapter) {
      throw new Error('No active LLM adapter is available');
    }

    // Create a minimal tool client that has no tools (direct LLM mode)
    const directToolClient = {
      getTools: () => [],
      callTool: async () => {
        throw new Error('Tool calls not available in direct LLM mode');
      }
    };

    // Process query without any tools
    const llmResult = await adapter.processQuery(query, directToolClient);
    console.log('[DIRECT LLM RESPONSE]', JSON.stringify(llmResult, null, 2));

    return {
      type: 'text',
      content: llmResult.content || 'No response available',
      data: null,
      toolResults: [],
      metadata: {
        llmProvider: activeLLM,
        mode: 'direct',
        timestamp: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('Direct query processing error:', error);
    return {
      type: 'error',
      content: `Error processing direct query: ${error.message}`,
      data: null,
      toolResults: [],
      metadata: {
        error: true,
        errorType: error.name,
        mode: 'direct',
        timestamp: new Date().toISOString()
      }
    };
  }
}

/**
 * Process a direct LLM chat with real streaming
 * @param {Array} messages - Array of messages
 * @param {string} modelName - Model name 
 * @returns {AsyncGenerator} Stream of text chunks
 */
async function* processDirectLLMChatStream(messages, modelName = null) {
  try {
    const adapter = getActiveProvider();
    if (!adapter) {
      throw new Error('No active LLM adapter is available');
    }

    // Only Google supports streaming for now
    if (activeLLM === 'google' && adapter.processChatSessionStream) {
      const MAX_CHAT_MESSAGES = 20;
      const limitedMessages = Array.isArray(messages) ? messages.slice(-MAX_CHAT_MESSAGES) : [];
      
      console.log('[STREAMING LLM CHAT] Starting stream with messages:', limitedMessages.length);
      
      // Yield chunks as they come from Gemini
      for await (const chunk of adapter.processChatSessionStream(limitedMessages, modelName)) {
        yield chunk;
      }
      
      console.log('[STREAMING LLM CHAT] Stream completed');
    } else {
      // Fallback to non-streaming for other providers
      const result = await processDirectLLMChat(messages, modelName);
      yield result.content || 'No response available';
    }

  } catch (error) {
    console.error('Streaming LLM chat processing error:', error);
    yield `Error processing chat: ${error.message}`;
  }
}

/**
 * Process a direct LLM chat without any MCP tools or IoT-specific prompts
 * @param {string} query - Natural language query
 * @param {string} modelName - Optional model name override
 * @returns {Promise<Object>} Direct LLM response
 */
async function processDirectLLMChat(messages, modelName = null) {
  try {
    const adapter = getActiveProvider();
    if (!adapter) {
      throw new Error('No active LLM adapter is available');
    }

    // Only Google supports chat session for now
    if (activeLLM === 'google' && adapter.processChatSession) {
      const MAX_CHAT_MESSAGES = 20;
      const limitedMessages = Array.isArray(messages) ? messages.slice(-MAX_CHAT_MESSAGES) : [];
      const result = await adapter.processChatSession(limitedMessages, modelName);
      console.log('[DIRECT LLM CHAT SESSION RESPONSE]', JSON.stringify(result, null, 2));
      return {
        type: 'text',
        content: result.content || 'No response available',
        data: null,
        toolResults: [],
        metadata: {
          llmProvider: activeLLM,
          mode: 'direct-chat',
          model: modelName || adapter.modelName,
          timestamp: new Date().toISOString()
        }
      };
    }

    // Fallback: single-turn for other providers
    // Extract latest user message
    const userMessages = messages?.filter(m => m.role === 'user') || [];
    const latestMessage = userMessages[userMessages.length - 1];
    const query = latestMessage?.content || '';

    // Create a specialized tool client for direct LLM chat (no tools, different prompt)
    const directChatClient = {
      getTools: () => [],
      callTool: async () => {
        throw new Error('Tool calls not available in direct LLM chat mode');
      },
      getSystemPrompt: () => getGeneralSystemPrompt() // Use general system prompt
    };

    // Create a temporary adapter instance with general system prompt if needed
    let chatAdapter = adapter;
    if (modelName && modelName !== adapter.modelName) {
      // Create a new adapter instance with the specified model
      if (activeLLM === 'anthropic' && anthropic) {
        chatAdapter = new AnthropicAdapter(anthropic, modelName, getGeneralSystemPrompt);
      }
    } else {
      // Use existing adapter but with general system prompt
      const originalGetSystemPrompt = chatAdapter.getSystemPrompt;
      chatAdapter.getSystemPrompt = getGeneralSystemPrompt;

      // Process the query
      const result = await chatAdapter.processQuery(query, directChatClient);

      // Restore original system prompt
      chatAdapter.getSystemPrompt = originalGetSystemPrompt;

      console.log('[DIRECT LLM CHAT RESPONSE]', JSON.stringify(result, null, 2));

      return {
        type: 'text',
        content: result.content || 'No response available',
        data: null,
        toolResults: [],
        metadata: {
          llmProvider: activeLLM,
          mode: 'direct-chat',
          model: modelName || adapter.modelName,
          timestamp: new Date().toISOString()
        }
      };
    }

    // For model override case (Anthropic)
    const result = await chatAdapter.processQuery(query, directChatClient);
    console.log('[DIRECT LLM CHAT RESPONSE]', JSON.stringify(result, null, 2));

    return {
      type: 'text',
      content: result.content || 'No response available',
      data: null,
      toolResults: [],
      metadata: {
        llmProvider: activeLLM,
        mode: 'direct-chat',
        model: modelName || adapter.modelName,
        timestamp: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('Direct LLM chat processing error:', error);
    return {
      type: 'error',
      content: `Error processing chat: ${error.message}`,
      data: null,
      toolResults: [],
      metadata: {
        error: true,
        errorType: error.name,
        mode: 'direct-chat',
        timestamp: new Date().toISOString()
      }
    };
  }
}

/**
 * Legacy query processing for backward compatibility
 * @param {string} query - Natural language query
 * @returns {Promise<Object>} Processed response
 */
async function processQueryLegacy(query) {
  try {
    const adapter = getActiveProvider();
    if (!adapter) {
      throw new Error('No active LLM adapter is available');
    }
    const llmResult = await adapter.processQuery(query, mcpClient);
    console.log('[LEGACY LLM ADAPTER RESPONSE]', JSON.stringify(llmResult, null, 2));
    return llmResult;
  } catch (error) {
    return {
      type: 'error',
      content: `Error processing query: ${error.message}`,
      data: null,
      chartConfig: undefined,
      meta: {}
    };
  }
}

/**
 * Process function calls from LLM response
 * @param {Object} response - LLM response
 * @param {string} query - User query
 * @param {Array} finalText - Accumulated text
 * @param {Array} toolResults - Tool call results
 * @returns {Promise<Array>} Tool results
 */
async function processFunctionCalls(response, query, finalText, toolResults) {
  try {
    console.log('=== FULL LLM RESPONSE ===');
    console.log(JSON.stringify(response, null, 2));
    console.log('========================');
    const candidate = response.candidates[0];
    if (candidate && candidate.content && candidate.content.parts) {
      console.log('=== RESPONSE PARTS ===');
      console.log(JSON.stringify(candidate.content.parts, null, 2));
      console.log('=====================');
      const functionCallParts = candidate.content.parts.filter(
        part => part.functionCall
      );
      if (functionCallParts.length > 0) {
        for (const part of functionCallParts) {
          const functionCall = part.functionCall;
          const toolName = functionCall.name;
          const toolInput = functionCall.args || {};
          try {
            const result = await mcpClient.callTool(toolName, toolInput);
            toolResults.push({
              name: toolName,
              result
            });
            await processToolResult(query, toolName, toolInput, result, finalText);
          } catch (toolError) {
            finalText.push(`Error executing tool ${toolName}: ${toolError.message}`);
          }
        }
        return true;
      }
    }
    return false;
  } catch (error) {
    return false;
  }
}

/**
 * Process a tool result
 * @param {string} query - User query
 * @param {string} toolName - Tool name
 * @param {Object} toolInput - Tool input
 * @param {Object} result - Tool result
 * @param {Array} finalText - Accumulated text
 * @returns {Promise<void>}
 */
async function processToolResult(query, toolName, toolInput, result, finalText) {
  try {
    // Log the raw, unprocessed result object for inspection
    console.log('[DEBUG] RAW MCP tool call result:', JSON.stringify(result, null, 2));
    const resultContent = mcpClient.extractContent(result);
    if (query.toLowerCase().includes("list") && query.includes("sensor")) {
      try {
        const lines = resultContent.split('\n').filter(line => line.trim());
        const sensors = [];
        for (let i = 1; i < lines.length; i++) {
          const parts = lines[i].split(',');
          if (parts.length >= 9) {
            const sensorName = parts[8].trim();
            if (sensorName && !sensors.includes(sensorName)) {
              sensors.push(sensorName);
            }
          }
        }
        if (sensors.length > 0) {
          finalText.push(`I found ${sensors.length} sensors in your system:`);
          sensors.forEach(sensor => {
            let description = "";
            if (sensor.includes("temp")) {
              description = "Temperature sensor";
            } else if (sensor.includes("hum")) {
              description = "Humidity sensor";
            } else if (sensor.includes("air_quality")) {
              description = "Air quality sensor";
            } else if (sensor.includes("unknown")) {
              description = "Unknown sensor type";
            } else {
              description = "Sensor";
            }
            finalText.push(`- **${sensor}**: ${description}`);
          });
          finalText.push("\nYou can query data from these sensors by asking questions like:");
          finalText.push("- \"What's the current temperature?\"");
          finalText.push("- \"Show me humidity data from the last hour\"");
          finalText.push("- \"Get air quality readings for the past day\"");
          return;
        }
      } catch (parseError) {}
    }
    if (resultContent && resultContent.trim() && resultContent.trim() !== '\r\n') {
      if (toolName === 'query-data') {
        // Log the actual result object for inspection
        console.log('[DEBUG] MCP tool call result for query-data:', JSON.stringify(result, null, 2));
        // Robustly extract CSV string from result
        let csv = '';
        if (result && result.content) {
          if (typeof result.content === 'string') {
            csv = result.content;
          } else if (Array.isArray(result.content) && result.content.length > 0) {
            const textObj = result.content.find(item => item.type === 'text' && item.text);
            if (textObj) csv = textObj.text;
          }
        }
        if (csv) {
          try {
            const lines = csv.split(/\r?\n/).filter(line => line.trim());
            if (lines.length > 1) {
          const headers = lines[0].split(',');
          const valueIndex = headers.findIndex(h => h.includes('_value'));
          const fieldIndex = headers.findIndex(h => h.includes('_field'));
            const values = [];
              let fieldName = 'sensor';
            for (let i = 1; i < lines.length; i++) {
              const parts = lines[i].split(',');
              if (parts.length > valueIndex) {
                values.push(parts[valueIndex]);
              }
                if (fieldIndex >= 0 && parts.length > fieldIndex) {
                fieldName = parts[fieldIndex];
              }
            }
            if (values.length === 1) {
              finalText.push(`The current ${fieldName} reading is ${values[0]}.`);
            } else if (values.length > 1) {
              const avg = values.reduce((sum, val) => sum + parseFloat(val), 0) / values.length;
              finalText.push(`I found ${values.length} ${fieldName} readings.`);
              finalText.push(`The average value is ${avg.toFixed(2)}.`);
              finalText.push(`The most recent reading is ${values[values.length - 1]}.`);
              } else {
                finalText.push('No valid readings found in the data.');
              }
              return;
            }
          } catch (parseError) {
            finalText.push('Error parsing CSV data from MCP tool result.');
          }
        } else {
          finalText.push('No CSV data found in MCP tool result.');
        }
      }
      if (toolName !== 'query-data') {
        finalText.push(`Here are the results for your query about "${query}":`);
      }
    } else {
      finalText.push(`I queried for ${query} but didn't find any data matching your criteria. This could mean:\n1. There's no data in the specified time range\n2. The sensor or measurement name might be different\n3. The data might be in a different bucket\n\nYou might want to try:\n- Using a wider time range (e.g., -24h or -7d)\n- Checking the exact sensor name with \"List all available sensors\"\n- Specifying a different bucket if applicable`);
    }
  } catch (error) {
    finalText.push(`Error processing tool result: ${error.message}`);
  }
}

/**
 * Format the LLM response
 * @param {string} query - User query
 * @param {Array} finalText - Accumulated text
 * @param {Array} toolResults - Tool call results
 * @returns {Object} Formatted response
 */
function formatResponse(query, finalText, toolResults) {
  const queryToolResult = toolResults.find(result =>
    result.name === 'query-data' ||
    result.name === 'query-data_influxdb' ||
    result.name === 'write-data_influxdb'
  );
  let responseType = 'text';
  let responseData = null;
  let chartConfig = undefined;
  let meta = {};
  
  if (queryToolResult && queryToolResult.result && queryToolResult.result.content) {
    responseType = 'influxdb_data';
    if (typeof queryToolResult.result.content === 'string') {
      responseData = queryToolResult.result.content;
    } else if (Array.isArray(queryToolResult.result.content) && queryToolResult.result.content.length === 1 && queryToolResult.result.content[0].type === 'text') {
      responseData = queryToolResult.result.content[0].text;
    } else {
      responseData = JSON.stringify(queryToolResult.result.content);
    }
    let fieldName = 'data';
    const queryLower = query.toLowerCase();
    if (queryLower.includes('humidity') || queryLower.includes(' hum ') || queryLower.includes('hum')) {
      fieldName = 'hum';
    } else if (queryLower.includes('temperature') || queryLower.includes(' temp ') || queryLower.includes('temp')) {
      fieldName = 'temp';
    } else if (queryLower.includes('air quality')) {
      fieldName = 'esp32/air_quality';
    }
    let timeRange = '1h';
    if (queryLower.includes('day') || queryLower.includes('24h')) {
      timeRange = '24h';
    } else if (queryLower.includes('week')) {
      timeRange = '7d';
    } else if (queryLower.includes('month')) {
      timeRange = '30d';
    }
    chartConfig = {
        title: `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} Data`,
        sensorType: fieldName,
        timeRange: timeRange
    };
    meta = { tool: queryToolResult.name };
  }
  
  const formattedResponse = {
    type: responseType,
    content: finalText.join('\n'),
    data: responseData,
    chartConfig,
    meta,
    // CRITICAL: Include the original toolResults for frontend data visualization
    toolResults: toolResults || []
  };
  
  return formattedResponse;
}

/**
 * Get enhanced MCP server manager
 * @returns {MCPServerManager} Server manager instance
 */
function getMCPServerManager() {
  initializeEnhancedComponents();
  return mcpServerManager;
}

/**
 * Get response standardizer
 * @returns {ResponseStandardizer} Response standardizer instance
 */
function getResponseStandardizer() {
  initializeEnhancedComponents();
  return responseStandardizer;
}

/**
 * Register a new MCP server
 * @param {Object} config - Server configuration
 * @returns {Promise<Object>} Registration result
 */
async function registerMCPServer(config) {
  initializeEnhancedComponents();
  return await mcpServerManager.registerServer(config);
}

/**
 * Get system statistics
 * @returns {Object} System statistics
 */
function getSystemStats() {
  initializeEnhancedComponents();
  return {
    mcpServers: mcpServerManager.getServerStats(),
    activeLLM,
    availableLLMs: Object.keys(providers),
    standardizerVersion: responseStandardizer ? '1.0.0' : 'not-initialized'
  };
}

/**
 * Shutdown enhanced components
 */
async function shutdown() {
  if (mcpServerManager) {
    await mcpServerManager.shutdown();
    mcpServerManager = null;
  }
  responseStandardizer = null;
}

module.exports = {
  processQuery,
  processDirectQuery,
  processDirectLLMChat,
  processDirectLLMChatStream,
  processFunctionCalls,
  processToolResult,
  formatResponse,
  getSystemPrompt,
  getGeneralSystemPrompt,
  providers, // Export for extensibility
  getActiveLLM,
  setActiveLLM,
  getMCPServerManager,
  getResponseStandardizer,
  registerMCPServer,
  getSystemStats,
  shutdown,
  initializeEnhancedComponents
};