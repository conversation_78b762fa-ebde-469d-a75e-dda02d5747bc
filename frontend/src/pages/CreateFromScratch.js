import React, { useState } from "react";
import {
  Container,
  <PERSON><PERSON><PERSON>,
  Button,
  Paper,
  TextField,
  Grid,
  Box,
  MenuItem,
  Snackbar,
  Alert,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Stack,
  Card,
  CardActionArea,
  CardContent
} from "@mui/material";
import SensorsIcon from '@mui/icons-material/Sensors';

const SENSOR_TYPES = [
  { label: "Temperature", objectId: "3303", key: "5700", type: "Sensor" },
  { label: "Humidity", objectId: "3304", key: "5700", type: "Sensor" },
  { label: "Light", objectId: "3301", key: "5700", type: "Sensor" },
  { label: "Pressure", objectId: "3323", key: "5700", type: "Sensor" },
  { label: "Actuator", objectId: "3306", key: "5850", type: "Actuator" },
  { label: "Accelerometer X", objectId: "3313", key: "5702", type: "Sensor" },
  { label: "Accelerometer Y", objectId: "3313", key: "5703", type: "Sensor" },
  { label: "Accelerometer Z", objectId: "3313", key: "5704", type: "Sensor" },
  { label: "Gyroscope X", objectId: "3334", key: "5702", type: "Sensor" },
  { label: "Gyroscope Y", objectId: "3334", key: "5703", type: "Sensor" },
  { label: "Gyroscope Z", objectId: "3334", key: "5704", type: "Sensor" },
  { label: "Magnetometer X", objectId: "3314", key: "5702", type: "Sensor" },
  { label: "Magnetometer Y", objectId: "3314", key: "5703", type: "Sensor" },
  { label: "Magnetometer Z", objectId: "3314", key: "5704", type: "Sensor" },
  { label: "CO2", objectId: "3325", key: "5700", type: "Sensor" },
  { label: "Voltage", objectId: "3315", key: "5700", type: "Sensor" },
  { label: "Current", objectId: "3316", key: "5700", type: "Sensor" },
  { label: "Power", objectId: "3317", key: "5700", type: "Sensor" },
  { label: "Battery", objectId: "3", key: "9", type: "Sensor" },
  { label: "Location Latitude", objectId: "6", key: "0", type: "Sensor" },
  { label: "Location Longitude", objectId: "6", key: "1", type: "Sensor" },
  { label: "Location Altitude", objectId: "6", key: "2", type: "Sensor" },
  { label: "Location Uncertainty", objectId: "6", key: "3", type: "Sensor" },
  { label: "Location Velocity", objectId: "6", key: "4", type: "Sensor" },
  { label: "Location Timestamp", objectId: "6", key: "5", type: "Sensor" },
  { label: "Location Speed", objectId: "6", key: "6", type: "Sensor" },
  { label: "Location Direction", objectId: "6", key: "7", type: "Sensor" },
  { label: "Custom", objectId: "", key: "", type: "Custom" }
];

const API_HOST = process.env.REACT_APP_API_HOST || 'localhost';
const API_BASE_URL = `http://${API_HOST}:5000`;

function CreateFromScratch() {
  const [deviceName, setDeviceName] = useState("");
  const [mqttBroker, setMqttBroker] = useState("");
  const [mqttPort, setMqttPort] = useState(1883);
  const [mqttQoS, setMqttQoS] = useState(2);
  const [serverHost, setServerHost] = useState("");
  const [serverPort, setServerPort] = useState(5683);
  const [devices, setDevices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [successDialog, setSuccessDialog] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });
  const [showSensorTypeDialog, setShowSensorTypeDialog] = useState(false);
  const [pendingSensor, setPendingSensor] = useState(null);

  // Show snackbar message
  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  // Close success dialog
  const handleCloseSuccessDialog = () => {
    setSuccessDialog(false);
  };

  const handleAddDeviceClick = () => setShowSensorTypeDialog(true);

  const handleSensorTypeSelect = (type) => {
    setShowSensorTypeDialog(false);
    if (type.label === "Custom") {
      setPendingSensor({ type: "Custom", objectId: "", key: "", instanceId: "", name: "", deviceName: "", processingFunction: "none" });
    } else {
      const count = devices.filter(d => d.objectId === type.objectId).length;
      setPendingSensor({
        type: type.type,
        objectId: type.objectId,
        key: type.key,
        instanceId: count.toString(),
        name: "",
        deviceName: "",
        processingFunction: "none"
      });
    }
  };

  const handlePendingSensorChange = (field, value) => {
    setPendingSensor({ ...pendingSensor, [field]: value });
  };

  const handleAddPendingSensor = () => {
    setDevices([...devices, {
      topic: pendingSensor.name,
      objectId: pendingSensor.objectId,
      resources: [{
        key: pendingSensor.key,
        value: "",
        name: pendingSensor.name,
        instanceId: pendingSensor.instanceId
      }],
      type: pendingSensor.type,
      processingFunction: pendingSensor.processingFunction,
      deviceName: pendingSensor.deviceName
    }]);
    setPendingSensor(null);
  };

  const handleCancelPendingSensor = () => {
    setPendingSensor(null);
  };

  const handleRemoveDevice = (index) => {
    const updated = [...devices];
    updated.splice(index, 1);
    setDevices(updated);
  };

  const handleCreate = async () => {
    // Validate required fields
    if (!deviceName) {
      showSnackbar('Please enter a virtual object name', 'error');
      return;
    }

    if (!mqttBroker) {
      showSnackbar('Please enter an MQTT broker address', 'error');
      return;
    }

    if (!serverHost) {
      showSnackbar('Please enter a server host address', 'error');
      return;
    }

    if (devices.length === 0) {
      showSnackbar('Please add at least one device', 'error');
      return;
    }

    // Check if any device has empty topic or objectId
    const invalidDevice = devices.find(device => !device.topic || !device.objectId);
    if (invalidDevice) {
      showSnackbar('Please fill in all device topics and object IDs', 'error');
      return;
    }

    const devicesPayload = devices.map(device => ({
      deviceName: device.deviceName,
      topic: device.topic,
      objectId: device.objectId,
      resources: device.resources.map((resource, idx) => ({
        key: resource.key,
        value: resource.value,
        name: resource.name,
        instanceId: resource.instanceId !== undefined ? resource.instanceId : idx.toString()
      })),
      processingFunction: device.processingFunction
    }));

    const payload = {
      virtualObjectName: deviceName,
      mqttBroker,
      mqttPort,
      mqttQoS,
      leshanServerHost: serverHost,
      leshanServerPort: serverPort,
      deviceType: pendingSensor?.type,  // Include the device type (sensor or actuator)
      sensors: devicesPayload
    };

    console.log(" Final payload to backend:", JSON.stringify(payload, null, 2));

    try {
      setLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/create-from-scratch`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        let errorMessage = errorData.message || `HTTP error! Status: ${response.status}`;

        // Extract specific error contexts
        if (errorMessage.includes("already in use")) {
          // Container name conflict error
          errorMessage = `Container name conflict: A virtual object with name "${deviceName}" already exists. Please choose a different name or remove the existing one.`;
        } else if (errorMessage.includes("ECONNREFUSED")) {
          // Connection refused error
          errorMessage = `Connection refused: Could not connect to the MQTT broker at ${mqttBroker}:${mqttPort}. Please check if the broker is running and accessible.`;
        } else if (errorMessage.includes("timeout")) {
          // Timeout error
          errorMessage = `Operation timed out: The server took too long to respond. This might be due to network issues or high server load.`;
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log("Deployment response:", data);

      // Show success message
      showSnackbar(`Virtual object ${deviceName} created successfully!`, 'success');
      setSuccessDialog(true);
    } catch (error) {
      console.error("Failed to fetch:", error);
      showSnackbar(`Error: ${error.message || 'Could not deploy virtual object'}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Paper elevation={3} sx={{ p: 4, backgroundColor: "#f8f9fa" }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: "bold", color: "#2c3e50" }}>
          Create Virtual Object
        </Typography>
        <Typography variant="subtitle1" color="text.secondary" paragraph>
          Configure a new virtual object from scratch.
        </Typography>

        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            Virtual Object Configuration
          </Typography>

          <TextField
            fullWidth
            label="Virtual Object Name"
            value={deviceName}
            onChange={(e) => setDeviceName(e.target.value)}
            margin="normal"
          />
          <TextField
            fullWidth
            label="MQTT Broker"
            value={mqttBroker}
            onChange={(e) => setMqttBroker(e.target.value)}
            margin="normal"
          />
          <TextField
            fullWidth
            label="MQTT Port"
            type="number"
            value={mqttPort}
            onChange={(e) => setMqttPort(e.target.value)}
            margin="normal"
          />
          <TextField
            select
            fullWidth
            label="MQTT QoS"
            value={mqttQoS}
            onChange={(e) => setMqttQoS(e.target.value)}
            margin="normal"
          >
            <MenuItem value={0}>0</MenuItem>
            <MenuItem value={1}>1</MenuItem>
            <MenuItem value={2}>2</MenuItem>
          </TextField>

          <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
            Leshan Client Configuration
          </Typography>
          <TextField
            fullWidth
            label="Server Host"
            value={serverHost}
            onChange={(e) => setServerHost(e.target.value)}
            margin="normal"
          />
          <TextField
            fullWidth
            label="Server Port"
            type="number"
            value={serverPort}
            onChange={(e) => setServerPort(e.target.value)}
            margin="normal"
          />

          <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
            Devices
          </Typography>

          {/* Pending Sensor Add Form */}
          {pendingSensor && (
            <Paper sx={{ p: 2, mb: 2, backgroundColor: "#f1f1f1" }}>
              <Typography variant="subtitle1" sx={{ mb: 2 }}>
                {pendingSensor.type === "Custom" ? "Custom Device" : `${pendingSensor.type} Device`}
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Name of Device"
                    value={pendingSensor.deviceName}
                    onChange={e => handlePendingSensorChange("deviceName", e.target.value)}
                    required
                  />
                </Grid>
                {pendingSensor.type !== "Custom" ? (
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="MQTT Topic"
                      value={pendingSensor.name}
                      onChange={e => handlePendingSensorChange("name", e.target.value)}
                      required
                    />
                  </Grid>
                ) : (
                  <>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        label="Object ID"
                        value={pendingSensor.objectId}
                        onChange={e => handlePendingSensorChange("objectId", e.target.value)}
                        required
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        label="Key"
                        value={pendingSensor.key}
                        onChange={e => handlePendingSensorChange("key", e.target.value)}
                        required
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        label="Instance ID"
                        value={pendingSensor.instanceId}
                        onChange={e => handlePendingSensorChange("instanceId", e.target.value)}
                        required
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        label="Resource Name (MQTT Topic)"
                        value={pendingSensor.name}
                        onChange={e => handlePendingSensorChange("name", e.target.value)}
                        required
                      />
                    </Grid>
                  </>
                )}
                {/* Always show Processing Function dropdown after Resource Name */}
                <Grid item xs={12}>
                  <TextField
                    select
                    fullWidth
                    label="Processing Function"
                    value={pendingSensor.processingFunction || 'none'}
                    onChange={e => handlePendingSensorChange("processingFunction", e.target.value)}
                  >
                    <MenuItem value="none">None</MenuItem>
                    <MenuItem value="average5">Average</MenuItem>
                    <MenuItem value="onchange">Send on Change</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12}>
                  <Button variant="contained" onClick={handleAddPendingSensor} sx={{ mr: 1 }}>
                    Add Device
                  </Button>
                  <Button variant="outlined" onClick={handleCancelPendingSensor} color="secondary">
                    Cancel
                  </Button>
                </Grid>
              </Grid>
            </Paper>
          )}

          {/* Device Summary Bars */}
          {devices.map((device, idx) => (
            <Paper key={idx} sx={{ p: 2, mb: 2, backgroundColor: "#e3f2fd", display: "flex", alignItems: "center", justifyContent: "space-between" }}>
              <Typography>
                {device.type ? `${device.type} Device, Name: ${device.deviceName || device.resources[0].name}` : `Device, Name: ${device.deviceName || device.resources[0].name}`}<br />
                <span style={{ fontSize: '0.95em', color: '#1976d2' }}>
                  Processing Function: {device.processingFunction === 'none' ? 'None' : device.processingFunction === 'average5' ? 'Average' : device.processingFunction === 'onchange' ? 'Send on Change' : device.processingFunction}
                </span>
              </Typography>
              <Button color="error" onClick={() => handleRemoveDevice(idx)}>
                Remove
              </Button>
            </Paper>
          ))}

          {/* Add Device Button */}
          {!pendingSensor && (
            <Button
              variant="outlined"
              color="primary"
              fullWidth
              sx={{ mb: 2 }}
              onClick={handleAddDeviceClick}
            >
              Add Device
            </Button>
          )}

          {/* Create Virtual Object Button */}
          {!pendingSensor && devices.length > 0 && (
            <Button
              variant="contained"
              color="primary"
              fullWidth
              onClick={handleCreate}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
            >
              {loading ? 'Creating...' : 'Create Virtual Object'}
            </Button>
          )}
        </Box>
      </Paper>

      {/* Success Dialog */}
      <Dialog open={successDialog} onClose={handleCloseSuccessDialog}>
        <DialogTitle>Success</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Virtual object {deviceName} has been created successfully!
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseSuccessDialog} color="primary">
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Loading Overlay */}
      {loading && (
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 9999,
          }}
        >
          <Paper sx={{ p: 4, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <CircularProgress size={60} sx={{ mb: 2 }} />
            <Typography variant="h6">Creating Virtual Object...</Typography>
          </Paper>
        </Box>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Sensor Type Dialog */}
      <Dialog open={showSensorTypeDialog} onClose={() => setShowSensorTypeDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Select Device Type</DialogTitle>
        <DialogContent dividers sx={{ maxHeight: 500 }}>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            {SENSOR_TYPES.map((type, idx) => (
              <Grid item xs={12} sm={4} md={2.4} key={type.label + idx}>
                <Card
                  sx={{
                    height: 110,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: 2,
                    borderRadius: 2,
                    transition: 'transform 0.2s, box-shadow 0.2s',
                    '&:hover': {
                      transform: 'scale(1.05)',
                      boxShadow: 6,
                      cursor: 'pointer',
                    },
                  }}
                  onClick={() => handleSensorTypeSelect(type)}
                >
                  <CardActionArea sx={{ height: '100%' }}>
                    <CardContent sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', p: 1 }}>
                      <SensorsIcon sx={{ fontSize: 32, color: '#1976d2', mb: 1 }} />
                      <Typography variant="body1" align="center" sx={{ fontWeight: 500 }}>
                        {type.label}
                      </Typography>
                    </CardContent>
                  </CardActionArea>
                </Card>
              </Grid>
            ))}
          </Grid>
        </DialogContent>
      </Dialog>
    </Container>
  );
}

export default CreateFromScratch;
