# MCP Dynamic Mode Configuration
USE_DYNAMIC_MCP=true
MCP_CONFIG_PATH=./mcp/config/servers.json

# InfluxDB Configuration
MCP_SERVER_PATH=/path/to/influxdb-mcp-server.js
INFLUXDB_TOKEN=your_influxdb_token
INFLUXDB_ORG=your_org
INFLUXDB_URL=http://localhost:8086

# PostgreSQL Configuration (if using PostgreSQL server)
POSTGRES_MCP_SERVER_PATH=/path/to/postgres-mcp-server.js
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=your_database
POSTGRES_USER=your_user
POSTGRES_PASSWORD=your_password

# MongoDB Configuration (if using MongoDB server)
MONGODB_MCP_SERVER_PATH=/path/to/mongodb-mcp-server.js
MONGODB_URI=mongodb://localhost:27017
MONGODB_DATABASE=your_database

# Weather API Configuration (if using weather server)
WEATHER_MCP_SERVER_PATH=/path/to/weather-mcp-server.js
OPENWEATHER_API_KEY=your_api_key

# Backup InfluxDB (if using backup server)
MCP_BACKUP_SERVER_PATH=/path/to/backup-influxdb-server.js
INFLUXDB_BACKUP_TOKEN=your_backup_token
INFLUXDB_BACKUP_URL=http://backup-server:8086
