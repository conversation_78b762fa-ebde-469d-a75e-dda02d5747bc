/**
 * MCP Module - Main Entry Point
 * Organized modular structure for Model Context Protocol functionality
 *
 * Structure:
 * - core/     : Core MCP client and bridge functionality
 * - llm/      : LLM providers and services
 * - tools/    : Tool definitions and implementations
 * - enhanced/ : Advanced MCP features
 * - config/   : Configuration and utilities
 */

// Import organized modules
const core = require('./core');
const llmService = require('./llm/service');
const toolRegistry = require('./tools/registry');
const supportedQueries = require('./config/supported-queries');

// Export organized structure
module.exports = {
  // Core MCP functionality
  core,

  // Direct access to key services (for backward compatibility)
  client: core.client,
  bridge: core.bridge,
  llmService,

  // Tool system
  tools: toolRegistry,

  // Configuration
  config: {
    supportedQueries
  },

  // Convenience methods (for backward compatibility)
  initialize: core.initialize,
  close: core.close,
  getTools: core.getTools,
  callTool: core.callTool,
  processQuery: core.processQuery,
  getActiveLLM: core.getActiveLLM,
  setActiveLLM: core.setActiveLLM
};
