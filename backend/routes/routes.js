const express = require("express");
const router = express.Router();
const multer = require("multer");
const path = require("path");
const controller = require("../controllers/new-control");
const templateController = require("../controllers/templateController");

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/',
  fileFilter: (req, file, cb) => {
    // Only accept JSON files
    if (path.extname(file.originalname) !== '.json') {
      return cb(new Error('Only JSON files are allowed'));
    }
    cb(null, true);
  }
});

// Device creation and deployment routes
router.post("/create-from-scratch", controller.deployContainer1);
router.post("/deploy", controller.deployContainer1);

// Template management routes
router.get("/templates", templateController.getAllTemplates);
router.get("/templates/:name/content", templateController.getTemplateContent);
router.post("/templates/upload", upload.single('file'), templateController.uploadTemplate);
router.delete("/templates/:name", templateController.deleteTemplate);
router.post("/temp-deploy", templateController.deployTemplate);

module.exports = router;