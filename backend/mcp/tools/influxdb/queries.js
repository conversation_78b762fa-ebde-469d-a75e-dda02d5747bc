/**
 * InfluxDB Query Tools
 * Handles data querying operations for InfluxDB
 */

const mcpClient = require('../../core/client'); // Import MCP client

/**
 * Query data from InfluxDB via MCP server
 * @param {Object} params - Query parameters
 * @returns {Promise<Object>} Query result
 */
async function queryData(params) {
  const { query, org } = params;
  try {
    // Use MCP client to call the 'query-data' tool
    if (!mcpClient.isConnected) {
      throw new Error('MCP client is not connected.');
    }
    const response = await mcpClient.callTool('query-data', { query, org });
    // Format the response to match the MCP server format
    return response;
  } catch (error) {
    console.error('Error querying data via MCP:', error.message);
    throw new Error(`Error querying data via MCP: ${error.message}`);
  }
}

/**
 * Write data to InfluxDB via MCP server
 * @param {Object} params - Write parameters
 * @returns {Promise<Object>} Write result
 */
async function writeData(params) {
  const { bucket, data, org, precision = 'ns' } = params;
  try {
    // Use MCP client to call the 'write-data' tool
    if (!mcpClient.isConnected) {
      throw new Error('MCP client is not connected.');
    }
    const response = await mcpClient.callTool('write-data', { bucket, data, org, precision });
    return response;
  } catch (error) {
    console.error('Error writing data via MCP:', error.message);
    throw new Error(`Error writing data via MCP: ${error.message}`);
  }
}

/**
 * Get query tool definitions
 * @returns {Array} Query tool definitions
 */
function getQueryTools() {
  return [
    {
      name: 'query-data',
      description: 'Query data from InfluxDB using Flux query language',
      inputSchema: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: 'Flux query string'
          },
          org: {
            type: 'string',
            description: 'The organization name'
          }
        },
        required: ['query', 'org']
      }
    },
    {
      name: 'write-data',
      description: 'Write data to InfluxDB in line protocol format',
      inputSchema: {
        type: 'object',
        properties: {
          bucket: {
            type: 'string',
            description: 'The bucket name'
          },
          data: {
            type: 'string',
            description: 'Data in InfluxDB line protocol format'
          },
          org: {
            type: 'string',
            description: 'The organization name'
          },
          precision: {
            type: 'string',
            description: 'Timestamp precision (ns, us, ms, s)',
            enum: ['ns', 'us', 'ms', 's']
          }
        },
        required: ['bucket', 'data', 'org']
      }
    }
  ];
}

module.exports = {
  queryData,
  writeData,
  getQueryTools
};
