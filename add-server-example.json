{"id": "mongodb-atlas", "name": "MongoDB Atlas Server", "description": "MongoDB Atlas MCP server for document database operations", "enabled": true, "connection": {"type": "stdio", "command": "node", "args": ["${MONGODB_MCP_SERVER_PATH}"], "timeout": 30000, "retryAttempts": 3, "retryDelay": 1000}, "authentication": {"type": "none", "envVars": [{"name": "MONGODB_URI", "fromEnv": "MONGODB_URI"}, {"name": "MONGODB_DATABASE", "fromEnv": "MONGODB_DATABASE"}]}, "capabilities": ["document-database", "nosql", "aggregation", "full-text-search"], "tools": [{"name": "find-documents", "description": "Find documents in MongoDB collection", "category": "data-access"}, {"name": "insert-document", "description": "Insert document into MongoDB collection", "category": "data-write"}, {"name": "aggregate-data", "description": "Run aggregation pipeline", "category": "analytics"}], "metadata": {"version": "1.0.0", "priority": 8, "tags": ["database", "nosql", "document", "mongodb"], "maintainer": "Database Team", "documentation": "https://docs.mongodb.com/"}, "healthCheck": {"enabled": true, "interval": 60000, "timeout": 10000, "method": "listTools"}}