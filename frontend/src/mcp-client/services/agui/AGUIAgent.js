/*
 * AG-UI Compatible Agent Implementation
 * Emits AG-UI protocol events as an Observable for the UI to consume
 * No UI state, no logging, no duplicate state management
 */

import { v4 as uuidv4 } from 'uuid';
import { Observable } from 'rxjs';

const API_HOST = process.env.REACT_APP_API_HOST || 'localhost';
const API_BASE_URL = `http://${API_HOST}:5000`;

export const EventType = {
  RUN_STARTED: 'RUN_STARTED',
  RUN_FINISHED: 'RUN_FINISHED',
  RUN_ERROR: 'RUN_ERROR',
  STEP_STARTED: 'STEP_STARTED',
  STEP_FINISHED: 'STEP_FINISHED',
  TEXT_MESSAGE_START: 'TEXT_MESSAGE_START',
  TEXT_MESSAGE_CONTENT: 'TEXT_MESSAGE_CONTENT',
  TEXT_MESSAGE_END: 'TEXT_MESSAGE_END',
  TOOL_CALL_START: 'TOOL_CALL_START',
  TOOL_CALL_ARGS: 'TOOL_CALL_ARGS',
  TOOL_CALL_END: 'TOOL_CALL_END',
  STATE_SNAPSHOT: 'STATE_SNAPSHOT',
  STATE_DELTA: 'STATE_DELTA',
  MESSAGES_SNAPSHOT: 'MESSAGES_SNAPSHOT',
  RAW: 'RAW',
  CUSTOM: 'CUSTOM'
};

export default class AGUIAgent {
  constructor(config = {}) {
    this.agentId = config.agentId || uuidv4();
    this.threadId = config.threadId || uuidv4();
    this.baseUrl = config.baseUrl || API_BASE_URL;
  }

  runAgent(input = {}) {
    const runId = input.runId || uuidv4();
    return new Observable(observer => {
      this.processQuery(input, runId, observer)
        .catch(error => {
          observer.next({
            type: EventType.RUN_ERROR,
            message: error.message,
            code: error.code || 'UNKNOWN_ERROR',
            timestamp: Date.now()
          });
          observer.error(error);
        });
    });
  }

  async processQuery(input, runId, observer) {
    try {
      const messages = input.messages || [];
      const userMessage = messages.filter(m => m.role === 'user').pop();
      if (!userMessage) throw new Error('No user message found');
      const queryMode = input.context?.queryMode || 'mcp';
      const endpoint = queryMode === 'llm' ? '/api/agui/llm-chat' : '/api/agui/query';
      const requestBody = {
        threadId: this.threadId,
        runId: runId,
        messages,
        tools: queryMode === 'mcp' ? (input.tools || []) : [],
        context: input.context || []
      };
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
        },
        body: JSON.stringify(requestBody)
      });
      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          buffer += decoder.decode(value, { stream: true });
          const events = buffer.split('\n\n');
          buffer = events.pop();
          for (const eventData of events) {
            if (eventData.trim() === '') continue;
            try {
              const dataMatch = eventData.match(/^data: (.+)$/m);
              if (!dataMatch) continue;
              const event = JSON.parse(dataMatch[1]);
              observer.next(event);
              if (event.type === EventType.RUN_FINISHED) {
                observer.complete();
                return;
              }
              if (event.type === EventType.RUN_ERROR) {
                observer.error(new Error(event.message));
                return;
              }
            } catch (parseError) {
              observer.next({
                type: EventType.RUN_ERROR,
                message: 'Event parse error',
                code: 'PARSE_ERROR',
                timestamp: Date.now()
              });
              observer.error(parseError);
              return;
            }
          }
        }
      } finally {
        reader.releaseLock();
        if (!observer.closed) observer.complete();
      }
    } catch (error) {
      observer.next({
        type: EventType.RUN_ERROR,
        message: error.message,
        code: error.code || 'PROCESS_QUERY_ERROR',
        timestamp: Date.now()
      });
      observer.error(error);
    }
  }
}
