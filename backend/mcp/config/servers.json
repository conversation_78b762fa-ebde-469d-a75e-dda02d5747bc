{"globalSettings": {"loadBalancing": "priority", "failover": true, "healthMonitoring": true, "logging": {"level": "info", "enableMetrics": true}}, "servers": [{"id": "influxdb-primary", "name": "InfluxDB Primary Server", "description": "Primary InfluxDB MCP server for time-series data operations", "enabled": true, "connection": {"type": "stdio", "command": "node", "args": ["${MCP_SERVER_PATH}"], "timeout": 30000, "retryAttempts": 3, "retryDelay": 1000}, "authentication": {"type": "none", "envVars": [{"name": "INFLUXDB_TOKEN", "fromEnv": "INFLUXDB_TOKEN"}, {"name": "INFLUXDB_ORG", "fromEnv": "INFLUXDB_ORG"}, {"name": "INFLUXDB_URL", "fromEnv": "INFLUXDB_URL"}]}, "capabilities": ["time-series", "data-query", "data-write", "analytics", "iot-data"], "tools": [{"name": "query-data", "description": "Query time-series data from InfluxDB", "category": "data-access"}, {"name": "write-data", "description": "Write data to InfluxDB", "category": "data-write"}, {"name": "create-bucket", "description": "Create a new InfluxDB bucket", "category": "management"}, {"name": "create-org", "description": "Create a new InfluxDB organization", "category": "management"}], "metadata": {"version": "1.0.0", "priority": 10, "tags": ["database", "time-series", "iot", "primary"], "maintainer": "Digital Twin Platform Team", "documentation": "https://docs.influxdata.com/"}, "healthCheck": {"enabled": true, "interval": 30000, "timeout": 5000, "method": "listTools"}}, {"id": "influxdb-backup", "name": "InfluxDB Backup Server", "description": "Backup InfluxDB MCP server for failover scenarios", "enabled": false, "connection": {"type": "stdio", "command": "node", "args": ["${MCP_BACKUP_SERVER_PATH}"], "timeout": 30000, "retryAttempts": 3, "retryDelay": 1000}, "authentication": {"type": "none", "envVars": [{"name": "INFLUXDB_TOKEN", "fromEnv": "INFLUXDB_BACKUP_TOKEN"}, {"name": "INFLUXDB_ORG", "fromEnv": "INFLUXDB_ORG"}, {"name": "INFLUXDB_URL", "fromEnv": "INFLUXDB_BACKUP_URL"}]}, "capabilities": ["time-series", "data-query", "backup", "iot-data"], "tools": [{"name": "query-data", "description": "Query time-series data from backup InfluxDB", "category": "data-access"}], "metadata": {"version": "1.0.0", "priority": 5, "tags": ["database", "time-series", "iot", "backup"], "maintainer": "Digital Twin Platform Team"}, "healthCheck": {"enabled": true, "interval": 60000, "timeout": 10000, "method": "listTools"}}, {"id": "weather-api", "name": "Weather Data API Server", "description": "External weather data MCP server for environmental context", "enabled": false, "connection": {"type": "http", "host": "api.weather.com", "port": 443, "path": "/mcp", "timeout": 15000, "retryAttempts": 2, "retryDelay": 2000}, "authentication": {"type": "token", "headers": {"Authorization": "Bearer ${WEATHER_API_TOKEN}"}}, "capabilities": ["weather-data", "environmental", "external-api"], "tools": [{"name": "get-weather", "description": "Get current weather data", "category": "environmental"}, {"name": "get-forecast", "description": "Get weather forecast", "category": "environmental"}], "metadata": {"version": "2.1.0", "priority": 3, "tags": ["weather", "external", "api"], "maintainer": "Weather Service Provider", "documentation": "https://api.weather.com/docs"}, "healthCheck": {"enabled": true, "interval": 120000, "timeout": 10000, "method": "ping"}}]}