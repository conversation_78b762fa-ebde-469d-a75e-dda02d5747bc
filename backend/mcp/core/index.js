/**
 * MCP Module - Main Entry Point
 * Provides organized access to all MCP functionality
 * Supports both legacy and dynamic MCP bridge implementations
 */
const mcpClient = require('./client');
const mcpBridge = require('./bridge');
const dynamicBridge = require('./dynamic-bridge');
const llmService = require('../llm/service');

// Determine which bridge to use based on environment
const USE_DYNAMIC_MCP = process.env.USE_DYNAMIC_MCP === 'true' || process.env.MCP_CONFIG_PATH;
const activeBridge = USE_DYNAMIC_MCP ? dynamicBridge : mcpBridge;

console.log(`Using ${USE_DYNAMIC_MCP ? 'Dynamic' : 'Legacy'} MCP Bridge`);

// Export the modules
module.exports = {
  // Core components
  client: mcpClient,
  bridge: mcpBridge,
  dynamicBridge: dynamicBridge,
  activeBridge: activeBridge,
  llmService: llmService,

  // Export key functions from active bridge for convenience
  initialize: activeBridge.initialize.bind(activeBridge),
  close: activeBridge.close.bind(activeBridge),
  getTools: activeBridge.getTools.bind(activeBridge),
  callTool: activeBridge.callTool.bind(activeBridge),
  processQuery: activeBridge.processQuery.bind(activeBridge),
  getActiveLLM: activeBridge.getActiveLLM.bind(activeBridge),
  setActiveLLM: activeBridge.setActiveLLM.bind(activeBridge),

  // Dynamic bridge specific methods (only available when using dynamic bridge)
  addServer: USE_DYNAMIC_MCP ? dynamicBridge.addServer.bind(dynamicBridge) : null,
  removeServer: USE_DYNAMIC_MCP ? dynamicBridge.removeServer.bind(dynamicBridge) : null,
  reloadConfiguration: USE_DYNAMIC_MCP ? dynamicBridge.reloadConfiguration.bind(dynamicBridge) : null,
  getConfiguration: USE_DYNAMIC_MCP ? dynamicBridge.getConfiguration.bind(dynamicBridge) : null,
  getServerStats: USE_DYNAMIC_MCP ? dynamicBridge.getServerStats.bind(dynamicBridge) : null,
  isReady: USE_DYNAMIC_MCP ? dynamicBridge.isReady.bind(dynamicBridge) : () => mcpClient.isConnected,

  // Utility properties
  isDynamic: USE_DYNAMIC_MCP,
  isLegacy: !USE_DYNAMIC_MCP
};
