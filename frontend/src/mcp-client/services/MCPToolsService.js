/**
 * MCP Tools Service
 * Provides access to all available MCP tools from the backend
 * Dynamically fetches and categorizes tools for the frontend
 */

const API_HOST = process.env.REACT_APP_API_HOST || 'localhost';
const API_BASE_URL = `http://${API_HOST}:5000`;

class MCPToolsService {
  constructor() {
    this.tools = [];
    this.toolCategories = {};
    this.isLoaded = false;
  }

  /**
   * Load available tools from the backend
   */
  async loadTools() {
    try {
      const response = await fetch(`${API_BASE_URL}/api/mcp/tools`);
      if (!response.ok) {
        throw new Error(`Failed to load tools: ${response.status}`);
      }
      
      const data = await response.json();
      this.tools = data.tools || [];
      this.categorizeTools();
      this.isLoaded = true;
      
      console.log('MCP Tools loaded:', this.tools.length);
      return this.tools;
    } catch (error) {
      console.error('Error loading MCP tools:', error);
      throw error;
    }
  }

  /**
   * Categorize tools by their functionality
   */
  categorizeTools() {
    this.toolCategories = {
      database: [],
      filesystem: [],
      analytics: [],
      management: [],
      other: []
    };

    this.tools.forEach(tool => {
      const category = this.getToolCategory(tool.name);
      if (this.toolCategories[category]) {
        this.toolCategories[category].push(tool);
      } else {
        this.toolCategories.other.push(tool);
      }
    });
  }

  /**
   * Determine tool category based on name
   */
  getToolCategory(toolName) {
    const categoryMap = {
      // Database tools
      'query-data': 'database',
      'write-data': 'database',
      'create-bucket': 'database',
      'create-org': 'database',
      
      // Filesystem tools
      'read_file': 'filesystem',
      'read_multiple_files': 'filesystem',
      'write_file': 'filesystem',
      'edit_file': 'filesystem',
      'create_directory': 'filesystem',
      'list_directory': 'filesystem',
      'directory_tree': 'filesystem',
      'move_file': 'filesystem',
      'search_files': 'filesystem',
      'get_file_info': 'filesystem',
      'list_allowed_directories': 'filesystem',
      
      // Analytics tools
      'analyze-trends': 'analytics',
      'generate-report': 'analytics',
      
      // Management tools
      'get-status': 'management',
      'health-check': 'management'
    };

    return categoryMap[toolName] || 'other';
  }

  /**
   * Get tools by category
   */
  getToolsByCategory(category) {
    return this.toolCategories[category] || [];
  }

  /**
   * Get all available tools
   */
  getAllTools() {
    return this.tools;
  }

  /**
   * Get tool by name
   */
  getTool(toolName) {
    return this.tools.find(tool => tool.name === toolName);
  }

  /**
   * Execute a tool via the backend
   */
  async executeTool(toolName, params = {}, requiredCapabilities = []) {
    try {
      const response = await fetch(`${API_BASE_URL}/api/mcp/test-tool`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          toolName,
          params,
          requiredCapabilities
        })
      });

      if (!response.ok) {
        throw new Error(`Tool execution failed: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error(`Error executing tool ${toolName}:`, error);
      throw error;
    }
  }

  /**
   * Get suggested tools for a query
   */
  getSuggestedTools(query) {
    const queryLower = query.toLowerCase();
    const suggestions = [];

    // Database-related queries
    if (queryLower.includes('data') || queryLower.includes('query') || 
        queryLower.includes('temperature') || queryLower.includes('sensor')) {
      suggestions.push(...this.getToolsByCategory('database'));
    }

    // File-related queries
    if (queryLower.includes('file') || queryLower.includes('read') || 
        queryLower.includes('write') || queryLower.includes('directory')) {
      suggestions.push(...this.getToolsByCategory('filesystem'));
    }

    // Analytics queries
    if (queryLower.includes('analyze') || queryLower.includes('trend') || 
        queryLower.includes('report')) {
      suggestions.push(...this.getToolsByCategory('analytics'));
    }

    return suggestions;
  }

  /**
   * Get tool usage examples
   */
  getToolExamples() {
    return {
      database: {
        'query-data': {
          description: 'Query sensor data from InfluxDB',
          example: {
            query: 'SELECT * FROM temperature WHERE time > now() - 1h',
            timeRange: '1h'
          }
        },
        'write-data': {
          description: 'Write data to InfluxDB',
          example: {
            bucket: 'sensors',
            data: 'temperature,location=room1 value=23.5'
          }
        }
      },
      filesystem: {
        'read_file': {
          description: 'Read a file from the filesystem',
          example: {
            path: '/home/<USER>/projects/config.json'
          }
        },
        'write_file': {
          description: 'Write content to a file',
          example: {
            path: '/home/<USER>/projects/output.txt',
            content: 'Hello, World!'
          }
        },
        'list_directory': {
          description: 'List files in a directory',
          example: {
            path: '/home/<USER>/projects'
          }
        }
      }
    };
  }

  /**
   * Check if tools are loaded
   */
  isReady() {
    return this.isLoaded;
  }

  /**
   * Get tool statistics
   */
  getStats() {
    return {
      totalTools: this.tools.length,
      categories: Object.keys(this.toolCategories).map(category => ({
        name: category,
        count: this.toolCategories[category].length
      }))
    };
  }
}

// Export singleton instance
export default new MCPToolsService();
