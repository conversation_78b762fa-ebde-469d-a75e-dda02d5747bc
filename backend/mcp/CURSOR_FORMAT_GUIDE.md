# Cursor/<PERSON>op Format Support

## Overview

Your Digital Twin Platform now supports **both** configuration formats:
- **Enhanced Format**: Full-featured with advanced options (load balancing, health monitoring, failover)
- **Cursor Format**: Compatible with <PERSON>ursor IDE and <PERSON>

## 🚀 Quick Start with Cursor Format

### Method 1: Direct Cursor Format Usage

Create a `cursor-config.json` file:
```json
{
  "mcpServers": {
    "influxdb": {
      "command": "npx",
      "args": ["influxdb-mcp-server"],
      "env": {
        "INFLUXDB_TOKEN": "your_token_here",
        "INFLUXDB_URL": "http://localhost:8086",
        "INFLUXDB_ORG": "your_org"
      }
    },
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    },
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem@latest"],
      "env": {
        "FILESYSTEM_ALLOWED_DIRECTORIES": "/home/<USER>/projects"
      }
    }
  }
}
```

### Method 2: Use Pre-built Examples

```bash
# Copy Cursor format example
cp mcp/config/examples/cursor-compatible.json my-cursor-config.json

# Set environment variable to use it
export MCP_CONFIG_PATH=./my-cursor-config.json
export USE_DYNAMIC_MCP=true
```

## 📋 Popular MCP Servers (Cursor Format)

### Database Servers
```json
{
  "mcpServers": {
    "influxdb": {
      "command": "npx",
      "args": ["influxdb-mcp-server"],
      "env": {
        "INFLUXDB_TOKEN": "your_token",
        "INFLUXDB_URL": "http://localhost:8086",
        "INFLUXDB_ORG": "your_org"
      }
    },
    "postgres": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-postgres@latest"],
      "env": {
        "POSTGRES_CONNECTION_STRING": "postgresql://user:pass@localhost:5432/db"
      }
    },
    "sqlite": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sqlite@latest"],
      "env": {
        "SQLITE_DATABASE_PATH": "/path/to/database.db"
      }
    }
  }
}
```

### Development Tools
```json
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem@latest"],
      "env": {
        "FILESYSTEM_ALLOWED_DIRECTORIES": "/home/<USER>/projects,/tmp"
      }
    },
    "github": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-github@latest"],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "your_github_token"
      }
    },
    "git": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-git@latest"]
    }
  }
}
```

### Search & Information
```json
{
  "mcpServers": {
    "brave-search": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-brave-search@latest"],
      "env": {
        "BRAVE_API_KEY": "your_brave_api_key"
      }
    },
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"],
      "env": {
        "UPSTASH_REDIS_REST_URL": "your_redis_url",
        "UPSTASH_REDIS_REST_TOKEN": "your_redis_token"
      }
    }
  }
}
```

### Web & Automation
```json
{
  "mcpServers": {
    "puppeteer": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-puppeteer@latest"]
    },
    "fetch": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-fetch@latest"]
    },
    "playwright": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-playwright@latest"]
    }
  }
}
```

## 🔄 Format Conversion

### Convert Cursor to Enhanced Format
```bash
# Via API
curl -X POST http://localhost:5000/api/mcp/convert \
  -H "Content-Type: application/json" \
  -d '{
    "config": {"mcpServers": {...}},
    "fromFormat": "cursor",
    "toFormat": "enhanced"
  }'
```

### Convert Enhanced to Cursor Format
```bash
# Via API
curl -X POST http://localhost:5000/api/mcp/convert \
  -H "Content-Type: application/json" \
  -d '{
    "config": {"servers": [...], "globalSettings": {...}},
    "fromFormat": "enhanced",
    "toFormat": "cursor"
  }'
```

## 🛠️ API Endpoints for Cursor Format

### Get Cursor Format Examples
```bash
# Get Cursor format example
curl http://localhost:5000/api/mcp/examples/cursor-format

# Get Enhanced format as Cursor format
curl "http://localhost:5000/api/mcp/examples/multi-database?format=cursor"
```

### Load Cursor Configuration
```bash
curl -X POST http://localhost:5000/api/mcp/load-cursor \
  -H "Content-Type: application/json" \
  -d '{
    "config": {
      "mcpServers": {
        "influxdb": {
          "command": "npx",
          "args": ["influxdb-mcp-server"],
          "env": {"INFLUXDB_TOKEN": "token"}
        }
      }
    }
  }'
```

### Check Supported Formats
```bash
curl http://localhost:5000/api/mcp/formats
```

## 🎯 Usage Scenarios

### Scenario 1: Cursor IDE User
If you're using Cursor IDE and want to use the same MCP configuration:

1. Copy your Cursor MCP config
2. Save it as `cursor-config.json`
3. Set `MCP_CONFIG_PATH=./cursor-config.json`
4. Enable dynamic mode: `USE_DYNAMIC_MCP=true`

### Scenario 2: Claude Desktop User
If you have a Claude Desktop configuration:

1. Copy from `~/Library/Application Support/Claude/claude_desktop_config.json` (macOS)
2. Or from `%APPDATA%\Claude\claude_desktop_config.json` (Windows)
3. Use it directly with our system

### Scenario 3: Mixed Environment
Use Enhanced format for production (with health monitoring, failover) and Cursor format for development:

```bash
# Development
export MCP_CONFIG_PATH=./dev-cursor-config.json

# Production  
export MCP_CONFIG_PATH=./prod-enhanced-config.json
```

## 🔧 Environment Variables

```bash
# Enable dynamic mode
export USE_DYNAMIC_MCP=true

# Specify config file (supports both formats)
export MCP_CONFIG_PATH=./your-config.json

# Format detection is automatic
# - Cursor format: has "mcpServers" property
# - Enhanced format: has "servers" or "globalSettings" properties
```

## ✅ Verification

### Test Your Configuration
```bash
# Check health
curl http://localhost:5000/api/mcp/health

# List servers and tools
curl http://localhost:5000/api/mcp/stats

# Test a tool
curl -X POST http://localhost:5000/api/mcp/test-tool \
  -H "Content-Type: application/json" \
  -d '{"toolName": "query-data", "params": {}}'
```

## 🎉 Benefits

### Cursor Format Benefits
- ✅ **Direct Compatibility**: Use existing Cursor/Claude Desktop configs
- ✅ **Simplicity**: Minimal configuration required
- ✅ **NPX Support**: Easy package management with npx
- ✅ **Community Standard**: Compatible with MCP ecosystem

### Enhanced Format Benefits  
- ✅ **Advanced Features**: Health monitoring, load balancing, failover
- ✅ **Production Ready**: Comprehensive error handling and monitoring
- ✅ **Scalability**: Unlimited servers with priority management
- ✅ **Flexibility**: Multiple connection types and authentication methods

## 🔄 Migration Path

1. **Start with Cursor Format**: Use your existing configuration
2. **Test Compatibility**: Verify all servers work correctly
3. **Gradual Enhancement**: Convert to Enhanced format for production features
4. **Hybrid Approach**: Use both formats for different environments

Your system now supports the industry-standard Cursor format while providing advanced enterprise features! 🚀
