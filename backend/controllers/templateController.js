const fs = require('fs');
const path = require('path');
const dockerService = require('../services/dockerService');

// Path to templates directory
const templatesDir = path.join(__dirname, '../templates');

// Get all templates
exports.getAllTemplates = (req, res) => {
  try {
    // Read all files in the templates directory
    const files = fs.readdirSync(templatesDir);

    // Filter for JSON files and create template objects
    const templates = files
      .filter(file => file.endsWith('.json'))
      .map(file => {
        const filePath = path.join(templatesDir, file);
        const stats = fs.statSync(filePath);

        return {
          name: path.basename(file, '.json'),
          size: stats.size,
          lastModified: stats.mtime
        };
      });

    res.json(templates);
  } catch (error) {
    console.error('Error getting templates:', error);
    res.status(500).json({ message: 'Failed to get templates', error: error.message });
  }
};

// Upload a new template
exports.uploadTemplate = (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    // The file should be available at req.file.path (temporary location)
    const tempPath = req.file.path;
    const targetPath = path.join(templatesDir, req.file.originalname);

    // Validate that the file is a valid JSON
    try {
      const fileContent = fs.readFileSync(tempPath, 'utf8');
      JSON.parse(fileContent); // This will throw if not valid JSON
    } catch (error) {
      return res.status(400).json({ message: 'Invalid JSON file' });
    }

    // Move the file to the templates directory
    fs.copyFileSync(tempPath, targetPath);
    fs.unlinkSync(tempPath); // Delete the temp file

    res.status(201).json({
      message: 'Template uploaded successfully',
      template: {
        name: path.basename(req.file.originalname, '.json'),
        size: req.file.size,
        lastModified: new Date()
      }
    });
  } catch (error) {
    console.error('Error uploading template:', error);
    res.status(500).json({ message: 'Failed to upload template', error: error.message });
  }
};

// Get template content
exports.getTemplateContent = (req, res) => {
  try {
    const templateName = req.params.name;
    const templatePath = path.join(templatesDir, `${templateName}.json`);

    // Check if the template exists
    if (!fs.existsSync(templatePath)) {
      return res.status(404).json({ message: 'Template not found' });
    }

    // Read the template file
    const templateContent = fs.readFileSync(templatePath, 'utf8');

    try {
      // Parse the template content to JSON
      const templateObj = JSON.parse(templateContent);
      res.json(templateObj);
    } catch (error) {
      res.status(400).json({ message: 'Invalid JSON template', error: error.message });
    }
  } catch (error) {
    console.error('Error getting template content:', error);
    res.status(500).json({ message: 'Failed to get template content', error: error.message });
  }
};

// Delete a template
exports.deleteTemplate = (req, res) => {
  try {
    const templateName = req.params.name;
    const templatePath = path.join(templatesDir, `${templateName}.json`);

    // Check if the template exists
    if (!fs.existsSync(templatePath)) {
      return res.status(404).json({ message: 'Template not found' });
    }

    // Delete the template file
    fs.unlinkSync(templatePath);

    res.json({ message: 'Template deleted successfully' });
  } catch (error) {
    console.error('Error deleting template:', error);
    res.status(500).json({ message: 'Failed to delete template', error: error.message });
  }
};

// Deploy a template
exports.deployTemplate = async (req, res) => {
  try {
    const {
      sensors = [],
      leshanObjects = {},
      containerName,
      leshanIpAddress,
      mqttBrokerIp,
      mqttBrokerPort,
      mqttUsername,
      mqttPassword,
      templateName
    } = req.body;

    if (!containerName) {
      return res.status(400).json({ message: 'Container name is required' });
    }
    if (!leshanIpAddress) {
      return res.status(400).json({ message: 'Leshan IP address is required' });
    }
    if (!templateName) {
      return res.status(400).json({ message: 'Template name is required' });
    }
    if (!/^[a-zA-Z0-9][a-zA-Z0-9_.-]*$/.test(containerName)) {
      return res.status(400).json({
        message: 'Invalid container name. Container names must contain only letters, numbers, underscores, periods, or hyphens, and must start with a letter or number.'
      });
    }

    // Load the previous template flow
    const templatePath = path.join(templatesDir, `${templateName}.json`);
    if (!fs.existsSync(templatePath)) {
      return res.status(404).json({ message: 'Template not found' });
    }
    const prevFlow = JSON.parse(fs.readFileSync(templatePath, 'utf8'));

    // --- Robust checks for template validity ---
    if (!Array.isArray(prevFlow) || prevFlow.length === 0) {
      return res.status(400).json({ message: 'Template is empty or malformed.' });
    }
    const hasTab = prevFlow.some(n => n.type === 'tab');
    const hasLeshan = prevFlow.some(n => n.type === 'lwm2m client');
    const hasSensor = prevFlow.some(n => n.type === 'mqtt in');
    if (!hasTab || !hasLeshan) {
      return res.status(400).json({ message: 'Template is missing required nodes (tab or lwm2m client).' });
    }
    // If no sensor nodes and no new sensors, error
    if (!hasSensor && sensors.length === 0) {
      return res.status(400).json({ message: 'Template has no sensor nodes and no new sensors to add.' });
    }
    // --- End robust checks ---

    // Build Leshan objects from sensors/resources
    const lwm2mObjects = {};
    sensors.forEach(sensor => {
      (sensor.resources || []).forEach(resource => {
        const objectId = sensor.objectId;
        const instanceId = resource.instanceId || sensor.instanceId || '0';
        if (!lwm2mObjects[objectId]) lwm2mObjects[objectId] = {};
        if (!lwm2mObjects[objectId][instanceId]) {
          lwm2mObjects[objectId][instanceId] = { "2": true };
        }
        lwm2mObjects[objectId][instanceId][resource.key] = {
          type: "FLOAT",
          acl: "RW",
          value: 0
        };
        lwm2mObjects[objectId][instanceId]["9999"] = {
          type: "STRING",
          acl: "R",
          value: sensor.leshanName
        };
      });
    });

    // Build sensor mapping for global context (uri -> topic)
    const sensorNamesMapping = {};
    sensors.forEach(sensor => {
      (sensor.resources || []).forEach(resource => {
        const objectId = sensor.objectId;
        const instanceId = resource.instanceId || sensor.instanceId || '0';
        const uri = `${objectId}/${instanceId}`;
        sensorNamesMapping[uri] = resource.name;
      });
    });

    // Helper: find node by topic and type
    function findNodeByTopicAndType(nodes, topic, type) {
      return nodes.find(n => n.type === type && n.topic === topic);
    }

    // Helper: find node by id
    function findNodeById(nodes, id) {
      return nodes.find(n => n.id === id);
    }

    // 1. Update existing sensor nodes (edit) by id
    let updatedFlow = [];
    for (const node of prevFlow) {
      // Only update MQTT/function/debug nodes for sensors in the request
      if (node.type === 'mqtt in' || node.type === 'function' || node.type === 'debug') {
        // Try to match by id
        const matchingSensor = sensors.find(s => s.id === node.id);
        if (matchingSensor) {
          // Update node properties
          if (node.type === 'mqtt in') {
            // Check if a processing function node exists after this MQTT node
            const processingFuncNodeId = `processing_func_${node.id}`;
            let processingFuncNode = prevFlow.find(n => n.id === processingFuncNodeId && n.type === 'function');
            // If not present, create and insert it
            if (!processingFuncNode) {
              // Find the main process function node this MQTT node was wired to
              const mainFuncNodeId = (node.wires && node.wires[0] && node.wires[0][0]) || null;
              // Create processing function node
              let processingFuncCode = 'return msg;';
              if (matchingSensor.processingFunction === 'average5') {
                processingFuncCode = `context.arr = context.arr || [];
context.arr.push(msg.payload);
if (context.arr.length > 5) context.arr.shift();
if (context.arr.length === 5) {
  msg.payload = context.arr.reduce((a, b) => a + b, 0) / 5;
  return msg;
}
return null;`;
              } else if (matchingSensor.processingFunction === 'onchange') {
                processingFuncCode = `let previousValue = context.get('previousValue');
if (msg.payload !== previousValue) {
    context.set('previousValue', msg.payload);
    return msg;
} else {
    return null;
}`;
              }
              processingFuncNode = {
                id: processingFuncNodeId,
                type: 'function',
                z: node.z,
                name: `Processing ${matchingSensor.resources && matchingSensor.resources[0]?.name || node.topic}`,
                func: processingFuncCode,
                outputs: 1,
                timeout: 0,
                noerr: 0,
                initialize: '',
                finalize: '',
                libs: [],
                x: (node.x || 370) + 110,
                y: node.y || 120,
                wires: mainFuncNodeId ? [[mainFuncNodeId]] : [[]]
              };
              // Insert the new node into the flow
              updatedFlow.push({
                ...node,
                topic: (matchingSensor.resources && matchingSensor.resources[0]?.name) || node.topic,
                broker: 'mqttBrokerConfig',
                wires: [[processingFuncNodeId]]
              });
              updatedFlow.push(processingFuncNode);
              continue;
            } else {
              // Update the processing function node code and wiring
              let processingFuncCode = 'return msg;';
              if (matchingSensor.processingFunction === 'average5') {
                processingFuncCode = `context.arr = context.arr || [];
context.arr.push(msg.payload);
if (context.arr.length > 5) context.arr.shift();
if (context.arr.length === 5) {
  msg.payload = context.arr.reduce((a, b) => a + b, 0) / 5;
  return msg;
}
return null;`;
              } else if (matchingSensor.processingFunction === 'onchange') {
                processingFuncCode = `let previousValue = context.get('previousValue');
if (msg.payload !== previousValue) {
    context.set('previousValue', msg.payload);
    return msg;
} else {
    return null;
}`;
              }
              processingFuncNode.func = processingFuncCode;
              processingFuncNode.name = `Processing ${matchingSensor.resources && matchingSensor.resources[0]?.name || node.topic}`;
              // Ensure the MQTT node is wired to the processing function node
              updatedFlow.push({
                ...node,
                topic: (matchingSensor.resources && matchingSensor.resources[0]?.name) || node.topic,
                broker: 'mqttBrokerConfig',
                wires: [[processingFuncNodeId]]
              });
              updatedFlow.push(processingFuncNode);
              continue;
            }
          }
          if (node.type === 'function') {
            // Only update the main process function node (not the processing function node)
            if (!node.id.startsWith('processing_func_')) {
              updatedFlow.push({
                ...node,
                name: `Process ${(matchingSensor.resources && matchingSensor.resources[0]?.name) || node.topic}`,
                func: `let sensorMappingsJson = env.get("SENSOR_MAPPINGS") || "{}";
let sensorMappings = {};
try {
    sensorMappings = JSON.parse(sensorMappingsJson);
} catch(e) {
    node.error("Failed to parse SENSOR_MAPPINGS environment variable: " + e.message);
    sensorMappings = {};
}
let incoming = msg.payload;
let topic = msg.topic; // This is the MQTT topic
let mapping = sensorMappings[topic] || {};
let fullLeshanUri = mapping.leshanUri || "Unknown/0/5700";
msg.payload = incoming || null;
msg.mqttTopic = topic; // Preserve the original MQTT topic
msg.topic = "/" + fullLeshanUri;
msg.deviceName = mapping.deviceName || topic;
return msg;`
              });
              continue;
            }
          }
          if (node.type === 'debug') {
            updatedFlow.push({
              ...node,
              name: `Debug Function ${(matchingSensor.resources && matchingSensor.resources[0]?.name) || node.topic}`
            });
            continue;
          }
        }
      }
      // For all other nodes, just copy as is
      updatedFlow.push(node);
    }

    // 2. Add new sensor nodes (add) only if id does not exist
    const existingNodeIds = new Set(updatedFlow.map(n => n.id));
    sensors.forEach((sensor, sensorIdx) => {
      if (!sensor.id || existingNodeIds.has(sensor.id)) return; // skip if id exists
      (sensor.resources || []).forEach((resource, resIdx) => {
        const unique = `${Date.now()}_${sensorIdx}_${resIdx}`;
        const topic = resource.name;
        const mqttNodeId = sensor.id || `mqtt_${unique}`;
        const processingFuncNodeId = `processing_func_${unique}`;
        const funcNodeId = `func_${unique}`;
        const debugNodeId = `debug_${unique}`;
        // MQTT node
        updatedFlow.push({
          id: mqttNodeId,
          type: "mqtt in",
          z: "flow1",
          name: `${containerName} - ${topic}`,
          topic: topic,
          qos: 2,
          datatype: "auto-detect",
          broker: "mqttBrokerConfig",
          nl: false,
          rap: true,
          rh: 0,
          inputs: 0,
          x: 370,
          y: 120 + (sensorIdx + resIdx) * 100,
          wires: [[processingFuncNodeId]]
        });
        // Processing function node
        let processingFuncCode = 'return msg;';
        if (sensor.processingFunction === 'average5') {
          processingFuncCode = `context.arr = context.arr || [];
context.arr.push(msg.payload);
if (context.arr.length > 5) context.arr.shift();
if (context.arr.length === 5) {
  msg.payload = context.arr.reduce((a, b) => a + b, 0) / 5;
  return msg;
}
return null;`;
        } else if (sensor.processingFunction === 'onchange') {
          processingFuncCode = `let previousValue = context.get('previousValue');
if (msg.payload !== previousValue) {
    context.set('previousValue', msg.payload);
    return msg;
} else {
    return null;
}`;
        }
        updatedFlow.push({
          id: processingFuncNodeId,
          type: "function",
          z: "flow1",
          name: `Processing ${topic}`,
          func: processingFuncCode,
          outputs: 1,
          timeout: 0,
          noerr: 0,
          initialize: "",
          finalize: "",
          libs: [],
          x: 480,
          y: 120 + (sensorIdx + resIdx) * 100,
          wires: [[funcNodeId]]
        });
        // Main process function node
        updatedFlow.push({
          id: funcNodeId,
          type: "function",
          z: "flow1",
          name: `Process ${topic}`,
          func: `let sensorMappingsJson = env.get("SENSOR_MAPPINGS") || "{}";
let sensorMappings = {};
try {
    sensorMappings = JSON.parse(sensorMappingsJson);
} catch(e) {
    node.error("Failed to parse SENSOR_MAPPINGS environment variable: " + e.message);
    sensorMappings = {};
}
let incoming = msg.payload;
let topic = msg.topic; // This is the MQTT topic
let mapping = sensorMappings[topic] || {};
let fullLeshanUri = mapping.leshanUri || "Unknown/0/5700";
msg.payload = incoming || null;
msg.mqttTopic = topic; // Preserve the original MQTT topic
msg.topic = "/" + fullLeshanUri;
msg.deviceName = mapping.deviceName || topic;
return msg;`,
          outputs: 1,
          timeout: 0,
          noerr: 0,
          initialize: "",
          finalize: "",
          libs: [],
          x: 580,
          y: 120 + (sensorIdx + resIdx) * 100,
          wires: [[debugNodeId, "lwm2m_client_out"]]
        });
        // Debug node
        updatedFlow.push({
          id: debugNodeId,
          type: "debug",
          z: "flow1",
          name: `Debug Function ${topic}`,
          active: true,
          tosidebar: true,
          console: false,
          tostatus: false,
          complete: "payload",
          targetType: "msg",
          statusVal: "",
          statusType: "auto",
          x: 600,
          y: 120 + (sensorIdx + resIdx) * 100,
          wires: []
        });
      });
    });

    // --- Build sensorMappings for all sensors (old and new) ---
    let sensorMappings = {};
    sensors.forEach(sensor => {
      (sensor.resources || []).forEach(resource => {
        if (!resource.name) return;
        const instanceId = resource.instanceId || sensor.instanceId || '0';
        const leshanUri = `${sensor.objectId}/${instanceId}/${resource.key}`;
        sensorMappings[resource.name] = {
          deviceName: sensor.leshanName,
          leshanUri
        };
      });
    });
    console.log('SENSOR_MAPPINGS to Docker:', JSON.stringify(sensorMappings, null, 2));

    // Update the lwm2m client node's objects property in the flow before writing the file
    console.log('Updating lwm2m client node objects property in flow...');
    let lwm2mClientNodeFound = false;
    for (let node of updatedFlow) {
      if (node.type === 'lwm2m client') {
        node.objects = JSON.stringify(lwm2mObjects);
        lwm2mClientNodeFound = true;
        console.log('lwm2m client node updated with objects:', node.objects);
      }
    }
    if (!lwm2mClientNodeFound) {
      console.warn('No lwm2m client node found in updatedFlow!');
    }

    // Write the merged flow to the container's directory
    const flowsDir = path.join(__dirname, "../flows_generated");
    const containerDir = path.join(flowsDir, `container-${containerName}-${Date.now()}`);
    if (!fs.existsSync(containerDir)) {
      fs.mkdirSync(containerDir, { recursive: true });
    }
    const flowPath = path.join(containerDir, 'flows.json');
    fs.writeFileSync(flowPath, JSON.stringify(updatedFlow, null, 2), "utf8");
    const flowTemplatePath = path.join(containerDir, 'flows.template.json');
    fs.writeFileSync(flowTemplatePath, JSON.stringify(updatedFlow, null, 2), "utf8");
    fs.writeFileSync(path.join(containerDir, 'sensorNames.json'), JSON.stringify(sensorNamesMapping, null, 2), "utf8");

    // Deploy the container
    const containerData = await dockerService.createAndStartContainer(containerName, flowPath, sensorMappings, leshanIpAddress);
    res.json(containerData);
  } catch (error) {
    console.error('Error deploying template:', error);
    res.status(500).json({ message: 'Failed to deploy template', error: error.message });
  }
};
