/**
 * Cursor Format Adapter
 * Converts between <PERSON><PERSON><PERSON>/<PERSON> MCP format and our enhanced format
 * Supports both directions: Cursor -> Enhanced and Enhanced -> Cursor
 */

const fs = require('fs');
const path = require('path');

class CursorFormatAdapter {
  constructor() {
    this.defaultPriorities = {
      'influxdb': 10,
      'postgres': 9,
      'sqlite': 8,
      'mongodb': 8,
      'filesystem': 7,
      'github': 6,
      'brave-search': 5,
      'fetch': 4,
      'puppeteer': 4,
      'memory': 3,
      'context7': 2
    };

    this.capabilityMapping = {
      'influxdb': ['time-series', 'data-query', 'data-write', 'analytics', 'iot-data'],
      'postgres': ['relational', 'sql-queries', 'data-access', 'analytics'],
      'sqlite': ['relational', 'sql-queries', 'local-storage', 'embedded-db'],
      'mongodb': ['document-database', 'nosql', 'aggregation', 'full-text-search'],
      'filesystem': ['file-operations', 'local-files', 'file-management'],
      'github': ['version-control', 'code-repository', 'issue-tracking', 'collaboration'],
      'brave-search': ['web-search', 'external-api', 'information-retrieval'],
      'fetch': ['http-requests', 'web-api', 'data-fetching'],
      'puppeteer': ['web-automation', 'browser-control', 'web-scraping'],
      'memory': ['data-storage', 'temporary-storage', 'session-management'],
      'context7': ['context-management', 'data-indexing', 'search']
    };
  }

  /**
   * Convert Cursor format to Enhanced format
   * @param {Object} cursorConfig - Cursor format configuration
   * @returns {Object} Enhanced format configuration
   */
  cursorToEnhanced(cursorConfig) {
    if (!cursorConfig.mcpServers) {
      throw new Error('Invalid Cursor format: missing mcpServers');
    }

    const enhancedConfig = {
      globalSettings: {
        loadBalancing: 'priority',
        failover: true,
        healthMonitoring: true,
        logging: {
          level: 'info',
          enableMetrics: true
        }
      },
      servers: []
    };

    // Convert each server
    Object.entries(cursorConfig.mcpServers).forEach(([serverId, serverConfig]) => {
      const enhancedServer = this.convertCursorServerToEnhanced(serverId, serverConfig);
      enhancedConfig.servers.push(enhancedServer);
    });

    // Sort by priority (highest first)
    enhancedConfig.servers.sort((a, b) => b.metadata.priority - a.metadata.priority);

    return enhancedConfig;
  }

  /**
   * Convert Enhanced format to Cursor format
   * @param {Object} enhancedConfig - Enhanced format configuration
   * @returns {Object} Cursor format configuration
   */
  enhancedToCursor(enhancedConfig) {
    const cursorConfig = {
      mcpServers: {}
    };

    enhancedConfig.servers.forEach(server => {
      if (server.enabled && server.connection.type === 'stdio') {
        const cursorServer = this.convertEnhancedServerToCursor(server);
        cursorConfig.mcpServers[server.id] = cursorServer;
      }
    });

    return cursorConfig;
  }

  /**
   * Convert single Cursor server to Enhanced format
   * @param {string} serverId - Server ID
   * @param {Object} cursorServer - Cursor server configuration
   * @returns {Object} Enhanced server configuration
   */
  convertCursorServerToEnhanced(serverId, cursorServer) {
    const priority = this.defaultPriorities[serverId] || 5;
    const capabilities = this.capabilityMapping[serverId] || ['general'];
    
    // Generate human-readable name
    const name = this.generateServerName(serverId);
    
    // Convert environment variables
    const envVars = [];
    if (cursorServer.env) {
      Object.entries(cursorServer.env).forEach(([key, value]) => {
        envVars.push({
          name: key,
          value: value
        });
      });
    }

    return {
      id: serverId,
      name: name,
      description: `${name} MCP server (converted from Cursor format)`,
      enabled: true,
      connection: {
        type: 'stdio',
        command: cursorServer.command,
        args: cursorServer.args || [],
        timeout: 30000,
        retryAttempts: 3,
        retryDelay: 1000
      },
      authentication: {
        type: 'none',
        envVars: envVars
      },
      capabilities: capabilities,
      tools: this.generateExpectedTools(serverId),
      metadata: {
        version: '1.0.0',
        priority: priority,
        tags: this.generateTags(serverId),
        maintainer: 'Cursor Format Adapter',
        documentation: this.getDocumentationUrl(serverId),
        originalFormat: 'cursor'
      },
      healthCheck: {
        enabled: true,
        interval: 30000,
        timeout: 5000,
        method: 'listTools'
      }
    };
  }

  /**
   * Convert Enhanced server to Cursor format
   * @param {Object} enhancedServer - Enhanced server configuration
   * @returns {Object} Cursor server configuration
   */
  convertEnhancedServerToCursor(enhancedServer) {
    const cursorServer = {
      command: enhancedServer.connection.command,
      args: enhancedServer.connection.args || []
    };

    // Convert environment variables
    if (enhancedServer.authentication?.envVars?.length > 0) {
      cursorServer.env = {};
      enhancedServer.authentication.envVars.forEach(envVar => {
        if (envVar.value) {
          cursorServer.env[envVar.name] = envVar.value;
        } else if (envVar.fromEnv) {
          cursorServer.env[envVar.name] = process.env[envVar.fromEnv] || '';
        }
      });
    }

    return cursorServer;
  }

  /**
   * Generate human-readable server name
   * @param {string} serverId - Server ID
   * @returns {string} Human-readable name
   */
  generateServerName(serverId) {
    const nameMap = {
      'influxdb': 'InfluxDB Time Series Database',
      'postgres': 'PostgreSQL Database',
      'sqlite': 'SQLite Database',
      'mongodb': 'MongoDB Document Database',
      'filesystem': 'File System Access',
      'github': 'GitHub Integration',
      'brave-search': 'Brave Search API',
      'fetch': 'HTTP Fetch Client',
      'puppeteer': 'Puppeteer Browser Automation',
      'memory': 'Memory Storage',
      'context7': 'Context7 Search Engine'
    };

    return nameMap[serverId] || serverId.charAt(0).toUpperCase() + serverId.slice(1);
  }

  /**
   * Generate expected tools for a server
   * @param {string} serverId - Server ID
   * @returns {Array} Expected tools
   */
  generateExpectedTools(serverId) {
    const toolsMap = {
      'influxdb': [
        { name: 'query-data', description: 'Query time-series data', category: 'data-access' },
        { name: 'write-data', description: 'Write time-series data', category: 'data-write' }
      ],
      'postgres': [
        { name: 'query', description: 'Execute SQL queries', category: 'data-access' },
        { name: 'execute', description: 'Execute SQL commands', category: 'data-write' }
      ],
      'filesystem': [
        { name: 'read_file', description: 'Read file contents', category: 'file-operations' },
        { name: 'write_file', description: 'Write file contents', category: 'file-operations' }
      ],
      'github': [
        { name: 'create_issue', description: 'Create GitHub issue', category: 'collaboration' },
        { name: 'search_repositories', description: 'Search repositories', category: 'search' }
      ]
    };

    return toolsMap[serverId] || [];
  }

  /**
   * Generate tags for a server
   * @param {string} serverId - Server ID
   * @returns {Array} Tags
   */
  generateTags(serverId) {
    const tagsMap = {
      'influxdb': ['database', 'time-series', 'iot', 'analytics'],
      'postgres': ['database', 'relational', 'sql'],
      'sqlite': ['database', 'embedded', 'local'],
      'mongodb': ['database', 'nosql', 'document'],
      'filesystem': ['files', 'local', 'storage'],
      'github': ['git', 'collaboration', 'code'],
      'brave-search': ['search', 'web', 'api'],
      'fetch': ['http', 'api', 'web'],
      'puppeteer': ['browser', 'automation', 'scraping'],
      'memory': ['storage', 'temporary', 'session'],
      'context7': ['search', 'context', 'indexing']
    };

    return tagsMap[serverId] || ['general'];
  }

  /**
   * Get documentation URL for a server
   * @param {string} serverId - Server ID
   * @returns {string} Documentation URL
   */
  getDocumentationUrl(serverId) {
    const docsMap = {
      'influxdb': 'https://docs.influxdata.com/',
      'postgres': 'https://www.postgresql.org/docs/',
      'sqlite': 'https://www.sqlite.org/docs.html',
      'mongodb': 'https://docs.mongodb.com/',
      'github': 'https://docs.github.com/',
      'brave-search': 'https://api.search.brave.com/app/documentation',
      'puppeteer': 'https://pptr.dev/'
    };

    return docsMap[serverId] || '';
  }

  /**
   * Load and convert Cursor format file
   * @param {string} filePath - Path to Cursor format file
   * @returns {Object} Enhanced format configuration
   */
  loadCursorConfig(filePath) {
    try {
      const cursorConfig = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      return this.cursorToEnhanced(cursorConfig);
    } catch (error) {
      throw new Error(`Failed to load Cursor config from ${filePath}: ${error.message}`);
    }
  }

  /**
   * Save Enhanced format as Cursor format
   * @param {Object} enhancedConfig - Enhanced configuration
   * @param {string} filePath - Output file path
   */
  saveAsCursorFormat(enhancedConfig, filePath) {
    try {
      const cursorConfig = this.enhancedToCursor(enhancedConfig);
      fs.writeFileSync(filePath, JSON.stringify(cursorConfig, null, 2));
    } catch (error) {
      throw new Error(`Failed to save Cursor config to ${filePath}: ${error.message}`);
    }
  }

  /**
   * Validate Cursor format configuration
   * @param {Object} config - Configuration to validate
   * @returns {boolean} Whether the configuration is valid
   */
  validateCursorFormat(config) {
    if (!config || typeof config !== 'object') {
      return false;
    }

    if (!config.mcpServers || typeof config.mcpServers !== 'object') {
      return false;
    }

    // Validate each server
    for (const [serverId, serverConfig] of Object.entries(config.mcpServers)) {
      if (!serverConfig.command || typeof serverConfig.command !== 'string') {
        return false;
      }

      if (serverConfig.args && !Array.isArray(serverConfig.args)) {
        return false;
      }

      if (serverConfig.env && typeof serverConfig.env !== 'object') {
        return false;
      }
    }

    return true;
  }
}

module.exports = CursorFormatAdapter;
