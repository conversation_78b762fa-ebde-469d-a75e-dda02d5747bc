{"$schema": "http://json-schema.org/draft-07/schema#", "title": "MCP Server Configuration Schema", "description": "JSON schema for defining MCP server configurations", "type": "object", "properties": {"servers": {"type": "array", "description": "Array of MCP server configurations", "items": {"$ref": "#/definitions/MCPServer"}}, "globalSettings": {"$ref": "#/definitions/GlobalSettings"}}, "required": ["servers"], "definitions": {"MCPServer": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the server", "pattern": "^[a-zA-Z0-9_-]+$"}, "name": {"type": "string", "description": "Human-readable name for the server"}, "description": {"type": "string", "description": "Description of the server's purpose"}, "enabled": {"type": "boolean", "description": "Whether the server is enabled", "default": true}, "connection": {"$ref": "#/definitions/ConnectionConfig"}, "authentication": {"$ref": "#/definitions/AuthConfig"}, "capabilities": {"type": "array", "description": "Server capabilities/tags", "items": {"type": "string"}}, "tools": {"type": "array", "description": "Expected tools provided by this server", "items": {"$ref": "#/definitions/ToolDefinition"}}, "metadata": {"$ref": "#/definitions/ServerMetadata"}, "healthCheck": {"$ref": "#/definitions/HealthCheckConfig"}}, "required": ["id", "name", "connection"]}, "ConnectionConfig": {"type": "object", "properties": {"type": {"type": "string", "enum": ["stdio", "http", "websocket", "tcp"], "description": "Connection type"}, "command": {"type": "string", "description": "Command to start the server (for stdio)"}, "args": {"type": "array", "description": "Command arguments", "items": {"type": "string"}}, "host": {"type": "string", "description": "Server host (for network connections)"}, "port": {"type": "integer", "description": "Server port (for network connections)"}, "path": {"type": "string", "description": "Server path or endpoint"}, "timeout": {"type": "integer", "description": "Connection timeout in milliseconds", "default": 30000}, "retryAttempts": {"type": "integer", "description": "Number of retry attempts", "default": 3}, "retryDelay": {"type": "integer", "description": "Delay between retries in milliseconds", "default": 1000}}, "required": ["type"]}, "AuthConfig": {"type": "object", "properties": {"type": {"type": "string", "enum": ["none", "token", "basic", "o<PERSON>h", "custom"], "default": "none"}, "token": {"type": "string", "description": "Authentication token"}, "username": {"type": "string", "description": "Username for basic auth"}, "password": {"type": "string", "description": "Password for basic auth"}, "headers": {"type": "object", "description": "Custom authentication headers", "additionalProperties": {"type": "string"}}, "envVars": {"type": "array", "description": "Environment variables to pass to the server", "items": {"type": "object", "properties": {"name": {"type": "string"}, "value": {"type": "string"}, "fromEnv": {"type": "string", "description": "Get value from environment variable"}}, "required": ["name"]}}}}, "ToolDefinition": {"type": "object", "properties": {"name": {"type": "string", "description": "Tool name"}, "description": {"type": "string", "description": "Tool description"}, "category": {"type": "string", "description": "Tool category"}, "parameters": {"type": "object", "description": "Tool parameter schema"}}, "required": ["name"]}, "ServerMetadata": {"type": "object", "properties": {"version": {"type": "string", "description": "Server version"}, "priority": {"type": "integer", "description": "Server priority (higher = preferred)", "default": 1}, "tags": {"type": "array", "description": "Server tags for categorization", "items": {"type": "string"}}, "maintainer": {"type": "string", "description": "Server maintainer"}, "documentation": {"type": "string", "description": "Link to server documentation"}}}, "HealthCheckConfig": {"type": "object", "properties": {"enabled": {"type": "boolean", "description": "Enable health checks", "default": true}, "interval": {"type": "integer", "description": "Health check interval in milliseconds", "default": 30000}, "timeout": {"type": "integer", "description": "Health check timeout in milliseconds", "default": 5000}, "method": {"type": "string", "enum": ["listTools", "ping", "custom"], "description": "Health check method", "default": "listTools"}}}, "GlobalSettings": {"type": "object", "properties": {"loadBalancing": {"type": "string", "enum": ["round-robin", "priority", "least-loaded", "random"], "description": "Load balancing strategy", "default": "priority"}, "failover": {"type": "boolean", "description": "Enable automatic failover", "default": true}, "healthMonitoring": {"type": "boolean", "description": "Enable health monitoring", "default": true}, "logging": {"type": "object", "properties": {"level": {"type": "string", "enum": ["debug", "info", "warn", "error"], "default": "info"}, "enableMetrics": {"type": "boolean", "default": true}}}}}}}