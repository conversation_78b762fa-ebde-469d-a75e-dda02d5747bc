const fs = require("fs");
const path = require("path");
const dockerService = require("../services/dockerService");

const flowsDir = path.join(__dirname, "../flows_generated");
if (!fs.existsSync(flowsDir)) {
  fs.mkdirSync(flowsDir);
}

exports.deployContainer1 = async (req, res) => {
  const {
    virtualObjectName,
    mqttBroker,
    mqttPort,
    mqttQoS,
    sensors = [],
    leshanServerHost,
    leshanServerPort,
    deviceType = "sensor", // Default to sensor if not specified
  } = req.body;

  try {
    // Create a unique directory for this container
    const containerDir = path.join(flowsDir, `container-${Date.now()}`);
    if (!fs.existsSync(containerDir)) {
      fs.mkdirSync(containerDir, { recursive: true });
    }

    // Write the flow file in the container's directory
    const flowPath = path.join(containerDir, 'flows.json');
    const templatePath = path.join(__dirname, "../flows.template.json");

    const templateFlow = JSON.parse(fs.readFileSync(templatePath, "utf8"));

    // Extract reusable nodes from template
    const baseMqttNode = templateFlow.find(n => n.type === "mqtt in");
    const baseFunctionNode = templateFlow.find(n => n.type === "function");
    const lwm2mOutNode = templateFlow.find(n => n.type === "lwm2m client out");
    const lwm2mClientNode = templateFlow.find(n => n.type === "lwm2m client");
    const mqttBrokerNode = templateFlow.find(n => n.type === "mqtt-broker");

    const flowId = baseMqttNode.z || "flow1";
    let yOffset = 120;
    let flow = [];
    const lwm2mObjects = {};

    // Create the mapping inside the function node
    const sensorMappings = {};
    sensors.forEach((sensor) => {
      sensor.resources.forEach((resource) => {
        if (!resource.name) return;
        const instanceId = resource.instanceId || sensor.instanceId || '0';
        const leshanURI = `${sensor.objectId}/${instanceId}/${resource.key}`;
        // Map MQTT topic to { deviceName, leshanUri }
        sensorMappings[resource.name] = {
          deviceName: sensor.deviceName,
          leshanUri: leshanURI
        };
      });
    });

    // Save the mapping as a JSON string in the SENSOR_MAPPINGS environment variable
    process.env.SENSOR_MAPPINGS = JSON.stringify(sensorMappings);

    // Track the total number of nodes for vertical positioning
    let totalNodeCount = 0;

    sensors.forEach((sensor, sensorIndex) => {
      // Process each resource individually
      sensor.resources.forEach((resource, resourceIndex) => {
        if (!resource.name) return; // Skip resources without names

        const uniqueSuffix = `${Date.now()}_${sensorIndex}_${resourceIndex}`;
        const resourceName = resource.name;
        // Use instanceId from resource or sensor, fallback to '0' if not present
        const instanceId = resource.instanceId || sensor.instanceId || '0';
        const leshanURI = `${sensor.objectId}/${instanceId}/${resource.key}`;

        // Create MQTT Node for this specific resource
        const mqttNode = {
          ...JSON.parse(JSON.stringify(baseMqttNode)),
          id: `mqtt_${uniqueSuffix}`,
          name: `${virtualObjectName} - ${resourceName}`,
          topic: resourceName,
          qos: mqttQoS,
          x: 160,
          y: yOffset + totalNodeCount * 100,
          wires: [[`func_${uniqueSuffix}`]],
        };

        // Create Debug Node for Function output
        const debugNode = {
          id: `debug_${uniqueSuffix}`,
          type: "debug",
          z: "flow1",
          name: `Debug ${resourceName}`,
          active: true,
          tosidebar: true,
          console: false,
          tostatus: false,
          complete: "payload",
          targetType: "msg",
          statusVal: "",
          statusType: "auto",
          x: 820,
          y: yOffset + totalNodeCount * 100,
          wires: []
        };

        // Determine function node code based on processingFunction
        let processingFunction = sensor.processingFunction || resource.processingFunction || 'none';
        let processingFuncCode = '';
        if (processingFunction === 'average5') {
          processingFuncCode = `
context.arr = context.arr || [];
context.arr.push(msg.payload);
if (context.arr.length > 5) context.arr.shift();
if (context.arr.length === 5) {
  msg.payload = context.arr.reduce((a, b) => a + b, 0) / 5;
  return msg;
}
return null;
`;
        } else if (processingFunction === 'onchange') {
          processingFuncCode = `
let previousValue = context.get('previousValue');
if (msg.payload !== previousValue) {
    context.set('previousValue', msg.payload);
    return msg;
} else {
    return null;
}
`;
        } else {
          processingFuncCode = 'return msg;';
        }
        const processingFuncNodeId = `processing_func_${uniqueSuffix}`;
        const processingFuncNode = {
          id: processingFuncNodeId,
          type: "function",
          z: flowId,
          name: `Processing ${resourceName}`,
          func: processingFuncCode,
          outputs: 1,
          noerr: 0,
          initialize: "",
          finalize: "",
          libs: [],
          x: 380,
          y: yOffset + totalNodeCount * 100,
          wires: [[`func_${uniqueSuffix}`]]
        };
        // Wire MQTT node to processing function node, then to main function node
        mqttNode.wires = [[processingFuncNodeId]];

        // Create Function Node
        const funcNode = {
          ...JSON.parse(JSON.stringify(baseFunctionNode)),
          id: `func_${uniqueSuffix}`,
          name: `Process ${resourceName}`,
          y: yOffset + totalNodeCount * 100,
          wires: [[`debug_${uniqueSuffix}`, lwm2mOutNode.id]],
          func: `
let sensorMappingsJson = env.get("SENSOR_MAPPINGS") || "{}";
let sensorMappings = {};
try {
    sensorMappings = JSON.parse(sensorMappingsJson);
} catch(e) {
    node.error("Failed to parse SENSOR_MAPPINGS environment variable: " + e.message);
    sensorMappings = {};
}
let incoming = msg.payload;
let topic = msg.topic; // This is the MQTT topic
let mapping = sensorMappings[topic] || {};
let fullLeshanUri = mapping.leshanUri || "Unknown/0/5700";
msg.payload = incoming || null;
msg.mqttTopic = topic; // Preserve the original MQTT topic
msg.topic = "/" + fullLeshanUri;
msg.deviceName = mapping.deviceName || topic;
return msg;
          `
        };

        // Add all nodes to the flow
        flow.push(mqttNode, processingFuncNode, debugNode, funcNode);

        // Increment the node count for vertical positioning
        totalNodeCount++;

        // Build LwM2M objects in the correct nested format
        const objectId = sensor.objectId;
        // Determine if this is an actuator (prefer per-device type, fallback to global deviceType)
        const isActuator = (sensor.type && sensor.type.toLowerCase() === 'actuator') || deviceType === 'actuator' || ["3306", "3311", "3312", "3342"].includes(objectId);
        if (!lwm2mObjects[objectId]) lwm2mObjects[objectId] = {};
        if (!lwm2mObjects[objectId][instanceId]) {
          lwm2mObjects[objectId][instanceId] = { "2": true };
        }
        lwm2mObjects[objectId][instanceId][resource.key] = {
          type: isActuator ? "BOOLEAN" : "FLOAT",
          acl: "RW",
          value: isActuator ? Boolean(Number(resource.value)) : (Number(resource.value) || 0)
        };
        // Add 9999 resource for name
        lwm2mObjects[objectId][instanceId]["9999"] = {
          type: "STRING",
          acl: "R",
          value: sensor.deviceName || resource.name
        };
      });
    });

    // Inject into LwM2M client node (must be stringified!)
    lwm2mClientNode.name = `${virtualObjectName} Leshan Client`;
    lwm2mClientNode.clientName = virtualObjectName;
    lwm2mClientNode.serverHost = leshanServerHost;
    lwm2mClientNode.serverPort = leshanServerPort;
    lwm2mClientNode.objects = JSON.stringify(lwm2mObjects);

    mqttBrokerNode.broker = mqttBroker;
    mqttBrokerNode.port = mqttPort;
    mqttBrokerNode.usetls = false;
    mqttBrokerNode.autoConnect = true;
    mqttBrokerNode.reconnectSec = "9";
    mqttBrokerNode.clientid = `node-red-${virtualObjectName}-${Date.now()}`;
    mqttBrokerNode.protocolVersion = "4"; // or "3" or "5" depending on your broker
    mqttBrokerNode.cleansession = true;
    mqttBrokerNode.willTopic = `${virtualObjectName}/status`;
    mqttBrokerNode.willPayload = "offline";
    mqttBrokerNode.credentials = {
      user: "darwin",
      password: "Darwin1!",
    };

    // Add Leshan client data fetching flow
    const leshanFlow = [
      {
        "id": "0dc9c107d9fc9b76",
        "type": "lwm2m client in",
        "z": "flow1",
        "name": "",
        "lwm2mClient": "lwm2m_client_in",
        "subscribeObjectEvents": true,
        "outputAsObject": false,
        "x": 890,
        "y": 160,
        "wires": [
            [
                "cf516c136c80ab05"
            ]
        ]
      },
      {
        "id": "cf516c136c80ab05",
        "type": "function",
        "z": "flow1",
        "name": "function 6",
        "func": "const token = 'Pb3x9HQY-VOLWGaZAlrif5ThJDJ6oSpJvUSJtD62NY3EC5pXKDdpYmJer1X4yt1C5T_aYi8MgEcKByqP1rPjFQ==';\nconst org = 'ICT';\nconst bucket = 'olive';\nconst host = 'http://**********:8086';\n\nlet incoming = msg.payload;\nlet leshanUriFromMsg = incoming.uri;  // e.g. '/3303/0/5700' or '3303/0/5700'\n\n// Normalize incoming.uri by removing leading slash if any\nif (leshanUriFromMsg.startsWith('/')) {\n    leshanUriFromMsg = leshanUriFromMsg.slice(1);\n}\n\nlet sensorMappingJson = env.get('SENSOR_MAPPINGS') || '{}';\nlet sensorMapping = {};\n\ntry {\n    sensorMapping = JSON.parse(sensorMappingJson);\n} catch (e) {\n    node.error('Failed to parse SENSOR_MAPPINGS: ' + e.message);\n    sensorMapping = {};\n}\n\n// Reverse lookup: find the deviceName for the matching leshanUri\nlet sensorName = 'UnknownSensor';\nfor (const [topic, mapping] of Object.entries(sensorMapping)) {\n    if (mapping.leshanUri === leshanUriFromMsg) {\n        sensorName = mapping.deviceName;\n        break;\n    }\n}\n\nlet timestamp = incoming.ts || Date.now();\nlet value = parseFloat(incoming.value.value);\n\nconst fieldKey = sensorName.toLowerCase();\n\nlet tags = 'sensor=' + sensorName + ',uri=' + incoming.uri + ',eventType=' + incoming.eventType;\nmsg.url = host + '/api/v2/write?org=' + org + '&bucket=' + bucket + '&precision=ns';\nmsg.headers = {\n  'Authorization': 'Token ' + token,\n  'Content-Type': 'text/plain'\n};\n\nmsg.payload = 'olive,' + tags + ' ' + fieldKey + '=' + value + ' ' + (timestamp * 1e6);\nreturn msg;",
        "outputs": 1,
        "timeout": 0,
        "noerr": 0,
        "initialize": "",
        "finalize": "",
        "libs": [],
        "x": 1020,
        "y": 160,
        "wires": [
            [
                "551e6840d9ed962f",
                "f3a219ccdf105ec5"
            ]
        ]
      },
      {
        "id": "551e6840d9ed962f",
        "type": "debug",
        "z": "flow1",
        "name": "debug 2",
        "active": true,
        "tosidebar": true,
        "console": false,
        "tostatus": false,
        "complete": "true",
        "targetType": "full",
        "statusVal": "",
        "statusType": "auto",
        "x": 1020,
        "y": 200,
        "wires": []
      },
      {
        "id": "lwm2m_client_in",
        "type": "lwm2m client",
        "z": "flow1",
        "disabled": false,
        "lazyStart": false,
        "clientName": virtualObjectName,
        "enableDTLS": false,
        "clientPort": "56830",
        "lifetimeSec": "300",
        "reconnectSec": "60",
        "bootstrapIntervalSec": "3600",
        "maxRecvPacketSize": "16486",
        "requestBootstrap": false,
        "saveProvisionedConfig": false,
        "useIPv4": true,
        "serverHost": leshanServerHost,
        "serverPort": leshanServerPort,
        "redirectLwm2mClientLog": false,
        "dumpLwm2mMessages": false,
        "hideSensitiveInfo": true,
        "propagateInternalEvents": false,
        "objects": JSON.stringify(lwm2mObjects)
      },
      {
        "id": "f3a219ccdf105ec5",
        "type": "http request",
        "z": "flow1",
        "name": "",
        "method": "POST",
        "ret": "txt",
        "paytoqs": "ignore",
        "url": "",
        "tls": "",
        "persist": false,
        "proxy": "",
        "insecureHTTPParser": false,
        "authType": "",
        "senderr": false,
        "headers": [
            {
                "keyType": "Content-Type",
                "keyValue": "",
                "valueType": "text/plain",
                "valueValue": ""
            }
        ],
        "x": 1190,
        "y": 160,
        "wires": [
            [
                "75c26e01925e8934"
            ]
        ]
      },
      {
        "id": "75c26e01925e8934",
        "type": "debug",
        "z": "flow1",
        "name": "debug 3",
        "active": true,
        "tosidebar": true,
        "console": false,
        "tostatus": false,
        "complete": "true",
        "targetType": "full",
        "statusVal": "",
        "statusType": "auto",
        "x": 1200,
        "y": 200,
        "wires": []
      }
    ];

    // Create a tab node to ensure the flow is properly structured
    const tabNode = {
      "id": "flow1",
      "type": "tab",
      "label": `${virtualObjectName} Flow`,
      "disabled": false,
      "info": ""
    };

    const finalFlow = [
      tabNode,
      ...flow,
      lwm2mOutNode,
      lwm2mClientNode,
      mqttBrokerNode,
      ...leshanFlow
    ];

    // Write the final flow to the container's directory
    fs.writeFileSync(flowPath, JSON.stringify(finalFlow, null, 2), "utf8");

    // Also write a template file for the entrypoint.sh script
    const flowTemplatePath = path.join(containerDir, 'flows.template.json');
    fs.writeFileSync(flowTemplatePath, JSON.stringify(finalFlow, null, 2), "utf8");

    const containerData = await dockerService.createAndStartContainer(virtualObjectName, flowPath, sensorMappings);
    res.json(containerData);
  } catch (error) {
    console.error("Error during deployment:", error);
    res.status(500).json({ error: "Deployment failed", details: error.message });
  }
};
