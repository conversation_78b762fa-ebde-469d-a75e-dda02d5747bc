const GoogleAdapter = class {
  constructor(googleAI, modelName, getSystemPrompt) {
    this.googleAI = googleAI;
    this.modelName = modelName;
    this.getSystemPrompt = getSystemPrompt;
  }

  async processQuery(query, toolClient) {
    if (!this.googleAI) {
      throw new Error('Google Gemini client is not initialized');
    }

    const tools = toolClient.getTools();
    let modelConfig = {
      model: this.modelName
    };

    // Only add tools configuration if tools are available
    if (tools && tools.length > 0) {
      console.log('[DEBUG] Processing tools for Google Gemini:', tools.length);

      const functionDeclarations = tools.map(tool => {
        console.log(`[DEBUG] Processing tool: ${tool.name}`);
        console.log(`[DEBUG] Tool schema:`, JSON.stringify(tool.inputSchema, null, 2));

        return {
          name: tool.name,
          description: tool.description || `${tool.name} tool for InfluxDB`,
          parameters: {
            type: "OBJECT",
            properties: Object.entries(tool.inputSchema.properties).reduce((acc, [key, value]) => {
              const param = {
                type: value.type.toUpperCase(),
                description: value.description || `${key} parameter for ${tool.name}`
              };

              // Handle array parameters - Google Gemini requires 'items' field for arrays
              if (value.type.toLowerCase() === 'array') {
                // Create clean items object without unsupported properties
                const items = value.items || { type: "string" };
                param.items = {
                  type: (items.type || "string").toUpperCase()
                };
                // Add description if available
                if (items.description) {
                  param.items.description = items.description;
                }
              }

              // Handle object parameters
              if (value.type.toLowerCase() === 'object' && value.properties) {
                // Recursively process object properties, filtering out unsupported fields
                param.properties = Object.entries(value.properties).reduce((objAcc, [objKey, objValue]) => {
                  objAcc[objKey] = {
                    type: (objValue.type || "string").toUpperCase(),
                    description: objValue.description || `${objKey} property`
                  };
                  return objAcc;
                }, {});
              }

              acc[key] = param;
              return acc;
            }, {}),
            required: tool.inputSchema.required
          }
        };
      });

      console.log('[DEBUG] Final function declarations:', JSON.stringify(functionDeclarations, null, 2));

      const googleTools = [{ functionDeclarations }];
      const functionCallingConfig = {
        mode: "ANY",
        allowedFunctionNames: tools.map(tool => tool.name)
      };

      modelConfig.tools = googleTools;
      modelConfig.toolConfig = { functionCallingConfig };
    }

    const model = this.googleAI.getGenerativeModel(modelConfig);

    // Use system prompt from tool client if available, otherwise use default
    const systemPrompt = toolClient.getSystemPrompt ? toolClient.getSystemPrompt() : this.getSystemPrompt();
    const prompt = `${systemPrompt}\n\nUser query: ${query}`;
    let conversationalContent = '';
    let responseData = null;
    let responseType = 'text';
    const finalText = [];
    const toolResults = [];
    try {
    const result = await model.generateContent({
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 1000
      }
    });
    const response = result.response;
      console.log('[DEBUG] Gemini non-streaming API response:', JSON.stringify(response, null, 2));
    // Extract text from the response
    try {
      const responseText = response.text();
      if (responseText && responseText.trim()) {
        finalText.push(responseText);
      }
    } catch (error) {}
    // Handle tool calls (functionCall) - only if tools are available
    if (tools && tools.length > 0) {
      const candidate = response.candidates && response.candidates[0];
      if (candidate && candidate.content && candidate.content.parts) {
        const functionCallParts = candidate.content.parts.filter(part => part.functionCall);
        for (const part of functionCallParts) {
          const functionCall = part.functionCall;
          const toolName = functionCall.name;
          const toolInput = functionCall.args || {};
          try {
            const result = await toolClient.callTool(toolName, toolInput);
            toolResults.push({ name: toolName, result });
          } catch (toolError) {
            finalText.push(`Error executing tool ${toolName}: ${toolError.message}`);
          }
        }
      }
    }
      conversationalContent = finalText.join('\n');
    // Check if we have data from tools
    const queryDataResult = toolResults.find(r => r.name === 'query-data');
    if (queryDataResult && queryDataResult.result && queryDataResult.result.content) {
      responseData = queryDataResult.result.content;
      // If we have data but no conversational response, create one
      if (!conversationalContent || conversationalContent.trim() === '') {
        conversationalContent = this.generateConversationalResponse(query, responseData);
      }
    }
    // Ensure we always have some response
    if (!conversationalContent || conversationalContent.trim() === '') {
      conversationalContent = "I understand your question, but I wasn't able to retrieve the specific data you requested. Could you try rephrasing your question or check if the data source is available?";
    }
    } catch (error) {
      conversationalContent = `Error during non-streaming: ${error.message}`;
    }
    return {
      type: responseType,
      content: conversationalContent,
      data: responseData,
      toolResults
    };
  }

  /**
   * Generate a conversational response when we have data but no LLM response
   */
  generateConversationalResponse(query, data) {
    const lowerQuery = query.toLowerCase();

    // Extract CSV string if data is an array of text objects
    let csv = '';
    if (typeof data === 'string') {
      csv = data;
    } else if (Array.isArray(data) && data.length > 0) {
      // Find the first non-empty text object
      const textObj = data.find(
        item => item.type === 'text' && item.text && item.text.trim().length > 0
      );
      if (textObj) {
        csv = textObj.text;
      }
    }

    let summaryLines = [];
    if (csv && csv.trim().length > 0 && csv.includes(',') && csv.includes('\n')) {
      try {
        const lines = csv.split(/\r?\n/).filter(line => line.trim());
        if (lines.length > 1) {
          const headers = lines[0].split(',').map(h => h.trim());
          const valueIndex = headers.findIndex(h => h === '_value');
          const fieldIndex = headers.findIndex(h => h === '_field');
          const timeIndex = headers.findIndex(h => h === '_time');
          const fieldStats = {};
          for (let i = 1; i < lines.length; i++) {
            const parts = lines[i].split(',');
            if (valueIndex >= 0 && fieldIndex >= 0 && parts.length > Math.max(valueIndex, fieldIndex)) {
              const field = parts[fieldIndex].trim();
              const value = parseFloat(parts[valueIndex]);
              const time = timeIndex >= 0 && parts.length > timeIndex ? parts[timeIndex].trim() : null;
              if (!isNaN(value)) {
                if (!fieldStats[field]) fieldStats[field] = { values: [], times: [] };
                fieldStats[field].values.push(value);
                if (time) fieldStats[field].times.push(time);
              }
            }
          }
          const fieldNames = Object.keys(fieldStats);
          if (fieldNames.length > 0) {
            for (const field of fieldNames) {
              const stats = fieldStats[field];
              const count = stats.values.length;
              const avg = stats.values.reduce((sum, v) => sum + v, 0) / count;
              const mostRecent = stats.values[count - 1];
              const mostRecentTime = stats.times.length > 0 ? stats.times[stats.times.length - 1] : '';
              summaryLines.push(
                `Field: ${field} — Count: ${count}, Average: ${avg.toFixed(2)}, Most Recent: ${mostRecent}` +
                (mostRecentTime ? ` at ${mostRecentTime}` : '')
              );
            }
          } else {
            summaryLines.push('No valid readings found in the data.');
          }
      } else {
          summaryLines.push('No data rows found in the CSV.');
        }
      } catch (e) {
        summaryLines.push('Error parsing CSV data from MCP tool result.');
      }
    } else {
      summaryLines.push('No data found (change the time range or query).');
    }

    // Add context based on query
    let context = '';
    if (lowerQuery.includes('temperature')) {
      context = ' (Temperature)';
    } else if (lowerQuery.includes('humidity')) {
      context = ' (Humidity)';
    } else if (lowerQuery.includes('current') || lowerQuery.includes('latest')) {
      context = ' (Most recent readings)';
    } else if (lowerQuery.includes('trend') || lowerQuery.includes('over time')) {
      context = ' (Trends)';
    } else if (lowerQuery.includes('sensor') || lowerQuery.includes('device')) {
      context = ' (Sensor network)';
    } else {
      context = ' (Data summary)';
    }

    return summaryLines.join('\n') + context;
  }

  /**
   * Process a full chat session with Gemini using real streaming
   * @param {Array} messages - Array of messages, each with {role, content}
   * @param {string} [modelName] - Optional model name override
   * @returns {AsyncGenerator} Stream of text chunks
   */
  async *processChatSessionStream(messages, modelName = null) {
    if (!this.googleAI) {
      throw new Error('Google Gemini client is not initialized');
    }
    
    const modelConfig = {
      model: modelName || this.modelName
    };
    const model = this.googleAI.getGenerativeModel(modelConfig);

    // Convert messages to Gemini format: {role, parts: [{text}]}
    const geminiMessages = messages.map(m => ({
      role: m.role,
      parts: [{ text: m.content }]
    }));

    try {
      const result = await model.generateContentStream({
        contents: geminiMessages,
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 1000
        }
      });
      
      // Yield each chunk as it comes from Gemini
      for await (const chunk of result.stream) {
        let text = '';
        if (typeof chunk.text === 'function') {
          text = chunk.text();
        } else if (typeof chunk.text === 'string') {
          text = chunk.text;
        }
        
        if (text && text.trim()) {
          yield text;
        }
      }
    } catch (error) {
      throw new Error(`Streaming error: ${error.message}`);
    }
  }

  /**
   * Process a full chat session with Gemini, sending the entire message history.
   * NON-STREAMING VERSION for use when we don't want duplicate streams.
   * @param {Array} messages - Array of messages, each with {role, content}
   * @param {string} [modelName] - Optional model name override
   * @returns {Promise<Object>} Direct LLM response with chat context
   */
  async processChatSession(messages, modelName = null) {
    if (!this.googleAI) {
      throw new Error('Google Gemini client is not initialized');
    }
    const modelConfig = {
      model: modelName || this.modelName
    };
    const model = this.googleAI.getGenerativeModel(modelConfig);

    // Convert messages to Gemini format: {role, parts: [{text}]}
    const geminiMessages = messages.map(m => ({
      role: m.role,
      parts: [{ text: m.content }]
    }));

    try {
      // Use NON-STREAMING generateContent to avoid dual responses
      const result = await model.generateContent({
        contents: geminiMessages,
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 1000
        }
      });
      
      const response = result.response;
      const conversationalContent = response.text();
      
      return {
        type: 'text',
        content: conversationalContent,
        data: null,
        toolResults: []
      };
    } catch (error) {
      return {
        type: 'text',
        content: `Error during chat session: ${error.message}`,
        data: null,
        toolResults: []
      };
    }
  }
};

module.exports = GoogleAdapter;