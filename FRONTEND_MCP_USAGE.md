# Frontend MCP Usage Guide

## Overview

Your Digital Twin Platform frontend now has comprehensive MCP (Model Context Protocol) integration that allows you to:

1. **Chat with AI** - Full conversational interface
2. **Quick Queries** - Fast natural language queries to your data
3. **Direct Tool Access** - Execute MCP tools directly

## Available MCP Tools

Based on your server logs, you have these tools available:

### Database Tools (InfluxDB)
- `write-data` - Write sensor data to InfluxDB
- `query-data` - Query time-series data from InfluxDB
- `create-bucket` - Create new InfluxDB buckets
- `create-org` - Create new InfluxDB organizations

### Filesystem Tools
- `read_file` - Read files from the filesystem
- `read_multiple_files` - Read multiple files at once
- `write_file` - Write content to files
- `edit_file` - Edit existing files
- `create_directory` - Create new directories
- `list_directory` - List directory contents
- `directory_tree` - Show directory structure
- `move_file` - Move/rename files
- `search_files` - Search for files
- `get_file_info` - Get file information
- `list_allowed_directories` - Show accessible directories

## How to Use

### 1. Chat Interface (Tab 1)
Full conversational AI interface with streaming responses.

**Example queries:**
- "Show me the latest temperature readings"
- "What files are in my projects folder?"
- "Create a summary report of today's sensor data"

### 2. Quick Query (Tab 2)
Fast, focused queries with tool execution display.

**Example queries:**
- "Read the config.json file"
- "List files in /home/<USER>/projects"
- "Query temperature data from last hour"
- "Write a test file with current timestamp"

### 3. Tools Panel (Tab 3)
Direct access to all MCP tools with parameter input.

**Features:**
- Browse tools by category
- See tool examples
- Execute tools with custom parameters
- View results immediately

## Natural Language Examples

### Database Queries
```
"Show me temperature data from the last hour"
"What's the average humidity today?"
"Write a new sensor reading: temperature 25.3°C in room1"
"Create a bucket called 'new-sensors'"
```

### File Operations
```
"Read the contents of config.json"
"List all files in the projects directory"
"Create a new file called test.txt with hello world"
"Show me the directory structure of /home/<USER>"
"Search for files containing 'sensor' in the name"
```

### Combined Operations
```
"Read the sensor config file and show me recent data for those sensors"
"Export today's temperature data to a CSV file"
"Create a backup directory and copy important config files there"
```

## Tool Parameters

### Database Tools
```json
// query-data
{
  "query": "SELECT * FROM temperature WHERE time > now() - 1h",
  "timeRange": "1h"
}

// write-data
{
  "bucket": "sensors",
  "data": "temperature,location=room1 value=23.5"
}
```

### Filesystem Tools
```json
// read_file
{
  "path": "/home/<USER>/projects/config.json"
}

// write_file
{
  "path": "/home/<USER>/projects/output.txt",
  "content": "Hello, World!"
}

// list_directory
{
  "path": "/home/<USER>/projects"
}
```

## Error Handling

The frontend handles these common errors:

1. **Server Connection Issues** - Shows retry options
2. **Invalid Tool Parameters** - Displays parameter validation errors
3. **Permission Errors** - Shows access denied messages
4. **Tool Execution Failures** - Displays detailed error information

## Tips for Best Results

1. **Be Specific** - "Read config.json from projects folder" vs "read file"
2. **Use Context** - "Show temperature data from the sensor in room1"
3. **Combine Operations** - "Read the config file and query data for those sensors"
4. **Check Tool Results** - Review the tool execution details in responses

## Troubleshooting

### Common Issues

**Problem**: "No MCP servers available"
**Solution**: Check that your backend server is running and MCP is configured

**Problem**: "Tool not found"
**Solution**: Verify the tool name and check available tools in the Tools panel

**Problem**: "Permission denied"
**Solution**: Check file paths and ensure they're in allowed directories

**Problem**: "Invalid parameters"
**Solution**: Use the Tools panel to see parameter examples

### Debug Information

The frontend logs all MCP events to the browser console. Open Developer Tools (F12) to see:
- Tool execution details
- Parameter validation
- Response data
- Error messages

## Integration Points

The MCP integration connects to these backend endpoints:

- `/api/agui/query` - Natural language queries with MCP tools
- `/api/mcp/tools` - List available tools
- `/api/mcp/test-tool` - Execute individual tools
- `/api/mcp/health` - Check MCP status

Your frontend is now fully integrated with your MCP backend! 🚀
