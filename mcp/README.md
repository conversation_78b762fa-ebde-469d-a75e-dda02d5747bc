# Simple MCP Setup Guide

## What is MCP?
MCP (Model Context Protocol) allows your AI to access external tools and data sources. In your Digital Twin Platform, it connects to InfluxDB to query sensor data.

## Current Setup
Your system uses **Cursor/Claude Desktop format** - the industry standard for MCP configuration.

## Configuration File
Location: `backend/mcp/config/servers.json`

```json
{
  "mcpServers": {
    "influxdb": {
      "command": "npx",
      "args": ["influxdb-mcp-server"],
      "env": {
        "INFLUXDB_TOKEN": "${INFLUXDB_TOKEN}",
        "INFLUXDB_URL": "${INFLUXDB_URL}",
        "INFLUXDB_ORG": "${INFLUXDB_ORG}"
      }
    }
  }
}
```

## Environment Variables
Set these in your `.env` file or environment:

```bash
# Required for InfluxDB MCP
INFLUXDB_TOKEN=your_influxdb_token_here
INFLUXDB_URL=http://localhost:8086
INFLUXDB_ORG=your_organization_name

# Enable dynamic MCP mode
USE_DYNAMIC_MCP=true
```

## How It Works

1. **AI Query**: User asks "Show me temperature data from last hour"
2. **MCP Bridge**: Converts natural language to InfluxDB query
3. **InfluxDB Server**: Executes query and returns data
4. **AI Response**: Formats data into human-readable response

## Adding New MCP Servers

To add a new MCP server, just add it to the `mcpServers` object:

```json
{
  "mcpServers": {
    "influxdb": {
      "command": "npx",
      "args": ["influxdb-mcp-server"],
      "env": {
        "INFLUXDB_TOKEN": "${INFLUXDB_TOKEN}",
        "INFLUXDB_URL": "${INFLUXDB_URL}",
        "INFLUXDB_ORG": "${INFLUXDB_ORG}"
      }
    },
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem@latest"],
      "env": {
        "FILESYSTEM_ALLOWED_DIRECTORIES": "/home/<USER>/projects"
      }
    }
  }
}
```

## Popular MCP Servers

- **InfluxDB**: Time-series database (already configured)
- **Filesystem**: File operations (`@modelcontextprotocol/server-filesystem@latest`)
- **GitHub**: Repository access (`@modelcontextprotocol/server-github@latest`)
- **PostgreSQL**: SQL database (`@modelcontextprotocol/server-postgres@latest`)
- **Brave Search**: Web search (`@modelcontextprotocol/server-brave-search@latest`)

## Testing

1. **Check if MCP is working**:
   ```bash
   curl http://localhost:5000/api/mcp/health
   ```

2. **List available tools**:
   ```bash
   curl http://localhost:5000/api/mcp/tools
   ```

3. **Test InfluxDB query**:
   ```bash
   curl -X POST http://localhost:5000/api/mcp/test-tool \
     -H "Content-Type: application/json" \
     -d '{"toolName": "query-data", "params": {"query": "SELECT * FROM temperature LIMIT 10"}}'
   ```

## Troubleshooting

**Problem**: MCP server not connecting
**Solution**: 
1. Check environment variables are set
2. Ensure InfluxDB is running
3. Verify token has correct permissions

**Problem**: "Tool not found" error
**Solution**: 
1. Check server is enabled in config
2. Restart the application
3. Verify MCP server package is installed

## API Endpoints

- `GET /api/mcp/health` - Check MCP status
- `GET /api/mcp/tools` - List available tools
- `POST /api/mcp/test-tool` - Test a specific tool
- `GET /api/mcp/config` - View current configuration

That's it! Your MCP setup is now simple and easy to understand.
