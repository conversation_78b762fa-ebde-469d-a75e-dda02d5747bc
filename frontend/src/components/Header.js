import React from 'react';
import { useNavigate } from 'react-router-dom';
import { BiCloudUpload } from 'react-icons/bi';
import { FaRocket } from 'react-icons/fa';
import { HiOutlineLightBulb } from 'react-icons/hi';
import { BiData } from 'react-icons/bi';
import { BiBrain } from 'react-icons/bi';

export default function Header() {
    const navigate = useNavigate();

    return (
        <header>
            <nav>
                <div className="logo" onClick={() => navigate('/')}>
                    <img src="/virt1.svg" alt="VirtuMesh Logo" style={{height: '50px', width: 'auto', marginRight: '0.5rem'}} />
                </div>
                <div className="nav-links">
                    <a href="#options"><BiData className="nav-icon" /> Solutions</a>
                    <a href="#how-it-works"><HiOutlineLightBulb className="nav-icon" /> How it Works</a>
                    <a href="#features"><FaRocket className="nav-icon" /> Features</a>
                    <button className="nav-btn" onClick={() => navigate('/chats')}><BiBrain  className="btn-icon" /> AI Assistant</button>
                    <button className="login-btn" onClick={() => navigate('/Login')}><BiCloudUpload className="btn-icon" /> Login</button>
                </div>
            </nav>
        </header>
    );
}
