# Simple MCP Setup for Digital Twin Platform

## What You Need to Know

Your Digital Twin Platform uses **MCP (Model Context Protocol)** to let AI query your InfluxDB sensor data. It's already configured and ready to use!

## Current Setup

**Configuration File**: `backend/mcp/config/servers.json`
```json
{
  "mcpServers": {
    "influxdb": {
      "command": "npx",
      "args": ["influxdb-mcp-server"],
      "env": {
        "INFLUXDB_TOKEN": "${INFLUXDB_TOKEN}",
        "INFLUXDB_URL": "${INFLUXDB_URL}",
        "INFLUXDB_ORG": "${INFLUXDB_ORG}"
      }
    }
  }
}
```

## Step 1: Set Environment Variables

Create a `.env` file in the `backend` directory:

```bash
# Copy the example file
cp backend/.env.example backend/.env

# Edit with your values
nano backend/.env
```

Required variables:
```bash
INFLUXDB_TOKEN=your_actual_influxdb_token
INFLUXDB_URL=http://localhost:8086
INFLUXDB_ORG=your_organization_name
USE_DYNAMIC_MCP=true
```

## Step 2: Start the Server

```bash
cd backend
npm start
```

## Step 3: Test MCP

```bash
# Check if MCP is working
curl http://localhost:5000/api/mcp/health

# Should return:
# {"status":"healthy","mode":"dynamic","isReady":true}
```

## How It Works

1. **User asks**: "Show me temperature data from the last hour"
2. **AI processes**: Converts to InfluxDB query
3. **MCP executes**: Runs query on your InfluxDB
4. **AI responds**: Formats data into readable answer

## Adding More MCP Servers

To add another MCP server, just add it to the `mcpServers` object:

```json
{
  "mcpServers": {
    "influxdb": {
      "command": "npx",
      "args": ["influxdb-mcp-server"],
      "env": {
        "INFLUXDB_TOKEN": "${INFLUXDB_TOKEN}",
        "INFLUXDB_URL": "${INFLUXDB_URL}",
        "INFLUXDB_ORG": "${INFLUXDB_ORG}"
      }
    },
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem@latest"],
      "env": {
        "FILESYSTEM_ALLOWED_DIRECTORIES": "/home/<USER>/projects"
      }
    }
  }
}
```

## Popular MCP Servers

- **Filesystem**: `@modelcontextprotocol/server-filesystem@latest`
- **GitHub**: `@modelcontextprotocol/server-github@latest`
- **PostgreSQL**: `@modelcontextprotocol/server-postgres@latest`
- **Brave Search**: `@modelcontextprotocol/server-brave-search@latest`

## Troubleshooting

**Problem**: "MCP server not connecting"
**Solution**: Check your environment variables in `.env` file

**Problem**: "Tool not found"
**Solution**: Restart the server after config changes

**Problem**: "InfluxDB connection failed"
**Solution**: Make sure InfluxDB is running and token is valid

## API Endpoints

- `GET /api/mcp/health` - Check status
- `GET /api/mcp/tools` - List available tools
- `POST /api/mcp/test-tool` - Test a tool

That's it! Your MCP is ready to use. 🚀
