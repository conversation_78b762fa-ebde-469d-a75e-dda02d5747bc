/**
 * InfluxDB Management Tools
 * Handles bucket and organization management operations
 */

const mcpClient = require('../../core/client'); // Import MCP client

/**
 * Create a new bucket in InfluxDB via MCP server
 * @param {Object} params - Bucket parameters
 * @returns {Promise<Object>} Create result
 */
async function createBucket(params) {
  const { name, orgID, retentionPeriodSeconds } = params;
  try {
    if (!mcpClient.isConnected) {
      throw new Error('MCP client is not connected.');
    }
    const response = await mcpClient.callTool('create-bucket', { name, orgID, retentionPeriodSeconds });
    return response;
  } catch (error) {
    console.error('Error creating bucket via MCP:', error.message);
    throw new Error(`Error creating bucket via MCP: ${error.message}`);
  }
}

/**
 * Create a new organization in InfluxDB via MCP server
 * @param {Object} params - Organization parameters
 * @returns {Promise<Object>} Create result
 */
async function createOrg(params) {
  const { name, description } = params;
  try {
    if (!mcpClient.isConnected) {
      throw new Error('MCP client is not connected.');
    }
    const response = await mcpClient.callTool('create-org', { name, description });
    return response;
  } catch (error) {
    console.error('Error creating organization via MCP:', error.message);
    throw new Error(`Error creating organization via MCP: ${error.message}`);
  }
}

/**
 * Get management tool definitions
 * @returns {Array} Management tool definitions
 */
function getManagementTools() {
  return [
    {
      name: 'create-bucket',
      description: 'Create a new bucket in InfluxDB',
      inputSchema: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            description: 'The bucket name'
          },
          orgID: {
            type: 'string',
            description: 'The organization ID'
          },
          retentionPeriodSeconds: {
            type: 'number',
            description: 'Retention period in seconds (optional)'
          }
        },
        required: ['name', 'orgID']
      }
    },
    {
      name: 'create-org',
      description: 'Create a new organization in InfluxDB',
      inputSchema: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            description: 'The organization name'
          },
          description: {
            type: 'string',
            description: 'Organization description (optional)'
          }
        },
        required: ['name']
      }
    }
  ];
}

module.exports = {
  createBucket,
  createOrg,
  getManagementTools
};
