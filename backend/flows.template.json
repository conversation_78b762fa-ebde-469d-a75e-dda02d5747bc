[{"id": "mqtt_node_1", "type": "mqtt in", "z": "flow1", "name": "{{VIRTUAL_OBJECT_NAME}} MQTT", "topic": "{{MQTT_TOPIC}}", "qos": "{{MQTT_QOS}}", "datatype": "auto-detect", "broker": "mqttBrokerConfig", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 370, "y": 220, "wires": [["function_node_1"]]}, {"id": "function_node_1", "type": "function", "z": "flow1", "name": "Process MQTT Data", "func": "return msg;", "outputs": 1, "timeout": 0, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 580, "y": 220, "wires": [["lwm2m_client_out"]]}, {"id": "lwm2m_client_out", "type": "lwm2m client out", "z": "flow1", "name": "", "lwm2mClient": "lwm2m_client_in", "x": 750, "y": 160, "wires": []}, {"id": "lwm2m_client_in", "type": "lwm2m client", "z": "flow1", "name": "{{VIRTUAL_OBJECT_NAME}} Leshan Client", "clientName": "{{CLIENT_NAME}}", "enableDTLS": false, "clientPort": "56830", "lifetimeSec": "300", "reconnectSec": "60", "bootstrapIntervalSec": "3600", "maxRecvPacketSize": "16486", "requestBootstrap": false, "saveProvisionedConfig": false, "useIPv4": true, "serverHost": "{{LESHAN_SERVER_HOST}}", "serverPort": "{{LESHAN_SERVER_PORT}}", "redirectLwm2mClientLog": false, "dumpLwm2mMessages": false, "hideSensitiveInfo": true, "propagateInternalEvents": false, "objects": "{}", "x": 750, "y": 220, "wires": []}, {"id": "mqttBrokerConfig", "type": "mqtt-broker", "name": "", "broker": "{{MQTT_BROKER}}", "port": "{{MQTT_PORT}}", "clientid": "", "autoConnect": true, "usetls": false, "protocolVersion": "4", "keepalive": "60", "cleansession": true, "autoUnsubscribe": true, "birthTopic": "", "birthQos": "0", "birthRetain": "false", "birthPayload": "", "birthMsg": {}, "closeTopic": "", "closeQos": "0", "closeRetain": "false", "closePayload": "", "closeMsg": {}, "willTopic": "", "willQos": "0", "willRetain": "false", "willPayload": "", "willMsg": {}, "userProps": "", "sessionExpiry": ""}]