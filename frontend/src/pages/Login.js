import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>a<PERSON><PERSON>, <PERSON><PERSON><PERSON>ock, <PERSON><PERSON><PERSON><PERSON>, FaEyeSlash, FaGoogle, FaGithub } from 'react-icons/fa';
import { BiArrowBack } from 'react-icons/bi';
import './Login.css';

export default function Login() {
    const [showPassword, setShowPassword] = useState(false);
    const navigate = useNavigate();

    const handleSubmit = (e) => {
        e.preventDefault();
        // Backend integration will be added later
        console.log('Login form submitted');
    };

    return (
        <div className="login-container">
            <div className="login-content">
                <div className="login-left">
                    <div className="login-brand">
                        <h1>Virtual Object</h1>
                        <p>Digital Twin Platform</p>
                    </div>
                    <div className="login-features">
                        <div className="feature-item">
                            <div className="feature-icon">🚀</div>
                            <div className="feature-text">
                                <h3>Real-time Monitoring</h3>
                                <p>Track your assets with live data updates</p>
                            </div>
                        </div>
                        <div className="feature-item">
                            <div className="feature-icon">📊</div>
                            <div className="feature-text">
                                <h3>Advanced Analytics</h3>
                                <p>Gain insights from comprehensive data analysis</p>
                            </div>
                        </div>
                        <div className="feature-item">
                            <div className="feature-icon">🛡️</div>
                            <div className="feature-text">
                                <h3>Secure Platform</h3>
                                <p>Enterprise-grade security for your data</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="login-right">
                    <div className="login-card">
                        <button className="back-button" onClick={() => navigate('/')}>
                            <BiArrowBack /> Back to Home
                        </button>
                        
                        <div className="login-header" style={{ padding: '8px 0', fontSize: '1.1rem', marginBottom: 8 }}>
                            <h1 style={{ fontSize: '1.3rem', margin: 0 }}>Welcome Back</h1>
                            <p style={{ fontSize: '1rem', margin: 0 }}>Please enter your credentials to continue</p>
                        </div>

                        <div className="social-login">
                            <button className="social-btn google">
                                <FaGoogle /> Continue with Google
                            </button>
                            <button className="social-btn github">
                                <FaGithub /> Continue with GitHub
                            </button>
                        </div>

                        <div className="divider">
                            <span>or</span>
                        </div>

                        <form onSubmit={handleSubmit} className="login-form">
                            <div className="form-group">
                                <label htmlFor="username">
                                    <FaUser className="input-icon" />
                                    Username
                                </label>
                                <input
                                    type="text"
                                    id="username"
                                    placeholder="Enter your username"
                                    required
                                />
                            </div>

                            <div className="form-group">
                                <label htmlFor="password">
                                    <FaLock className="input-icon" />
                                    Password
                                </label>
                                <div className="password-input">
                                    <input
                                        type={showPassword ? "text" : "password"}
                                        id="password"
                                        placeholder="Enter your password"
                                        required
                                    />
                    <button
                                        type="button"
                                        className="toggle-password"
                                        onClick={() => setShowPassword(!showPassword)}
                                    >
                                        {showPassword ? <FaEyeSlash /> : <FaEye />}
                                    </button>
                                </div>
                            </div>

                            <div className="form-options">
                                <label className="remember-me">
                                    <input type="checkbox" />
                                    <span>Remember me</span>
                                </label>
                                <a href="#" className="forgot-password">Forgot Password?</a>
                            </div>

                            <button type="submit" className="login-button">
                                Sign In
                    </button>
                </form>

                        <div className="login-footer" style={{ padding: '6px 0', fontSize: '0.95rem', marginTop: 8 }}>
                            <p style={{ margin: 0 }}>Don't have an account? <a href="#">Sign Up</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
