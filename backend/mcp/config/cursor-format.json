{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "influxdb": {"command": "npx", "args": ["influxdb-mcp-server"], "env": {"INFLUXDB_TOKEN": "Pb=", "INFLUXDB_URL": "http://localhost:8086", "INFLUXDB_ORG": "ICT"}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem@latest"], "env": {"FILESYSTEM_ALLOWED_DIRECTORIES": "/home/<USER>/projects"}}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search@latest"], "env": {"BRAVE_API_KEY": "your_brave_api_key_here"}}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres@latest"], "env": {"POSTGRES_CONNECTION_STRING": "postgresql://user:password@localhost:5432/dbname"}}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github@latest"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "your_github_token_here"}}, "sqlite": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite@latest"], "env": {"SQLITE_DATABASE_PATH": "/path/to/your/database.db"}}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer@latest"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory@latest"]}, "fetch": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-fetch@latest"]}}}