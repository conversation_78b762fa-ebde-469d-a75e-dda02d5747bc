#!/bin/bash
# Wait for a moment to ensure all systems are ready
#sleep 5
echo "[entrypoint] Sleeping for 5 seconds to allow dependent services to initialize..."

# Substitute the environment variables in the template to create the active flows file.
if [ -f "/data/flows.template.json" ]; then
  envsubst < /data/flows.template.json > /data/flows.json
  echo "Generated flows.json from template"
else
  echo "No template found, using existing flows.json"
fi

# Optional: Print out the generated flows file for debugging
cat /data/flows.json
echo "[entrypoint] Launching Node-RED..."
# Start Node-RED with explicit settings
npm start -- --userDir /data
#sleep 5