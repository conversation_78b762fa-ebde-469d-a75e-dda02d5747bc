/**
 * MCP Configuration Management API Routes
 * Provides REST endpoints for managing MCP server configurations
 */

const express = require('express');
const router = express.Router();
const MCPConfigLoader = require('../config/config-loader');
const fs = require('fs');
const path = require('path');

// Get MCP system safely to avoid circular dependencies
function getMCPSystem() {
  try {
    return require('../core');
  } catch (error) {
    console.error('Error loading MCP system:', error);
    return null;
  }
}

/**
 * Get current MCP configuration
 */
router.get('/config', (req, res) => {
  try {
    const mcp = getMCPSystem();
    if (!mcp) {
      return res.status(500).json({
        error: 'MCP system not available'
      });
    }

    if (!mcp.isDynamic) {
      return res.status(400).json({
        error: 'Configuration management is only available in dynamic mode',
        suggestion: 'Set USE_DYNAMIC_MCP=true or MCP_CONFIG_PATH environment variable'
      });
    }

    const config = mcp.getConfiguration();
    const stats = mcp.getServerStats();

    res.json({
      config,
      stats,
      mode: 'dynamic',
      isReady: mcp.isReady()
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to get configuration',
      message: error.message
    });
  }
});

/**
 * Get server statistics
 */
router.get('/stats', (req, res) => {
  try {
    if (!mcp.isDynamic) {
      return res.json({
        mode: 'legacy',
        isConnected: mcp.client?.isConnected || false,
        tools: mcp.getTools?.() || []
      });
    }

    const stats = mcp.getServerStats();
    res.json({
      ...stats,
      mode: 'dynamic',
      isReady: mcp.isReady()
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to get statistics',
      message: error.message
    });
  }
});

/**
 * Get available tools from all servers
 */
router.get('/tools', (req, res) => {
  try {
    const mcp = getMCPSystem();

    // Get tools safely with fallback
    let tools = [];
    let mode = 'unknown';

    if (mcp && typeof mcp.getTools === 'function') {
      tools = mcp.getTools();
      mode = mcp.isDynamic ? 'dynamic' : 'legacy';
    } else if (mcp && mcp.activeBridge && typeof mcp.activeBridge.getTools === 'function') {
      tools = mcp.activeBridge.getTools();
      mode = mcp.isDynamic ? 'dynamic' : 'legacy';
    } else {
      // Fallback to legacy client
      const mcpClient = require('../core/client');
      if (mcpClient && mcpClient.isConnected) {
        tools = mcpClient.getTools ? mcpClient.getTools() : [];
        mode = 'legacy-fallback';
      }
    }

    res.json({
      tools,
      count: tools.length,
      mode,
      isReady: mcp && mcp.isReady ? mcp.isReady() : false
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to get tools',
      message: error.message
    });
  }
});

/**
 * Add a new server (dynamic mode only)
 */
router.post('/servers', async (req, res) => {
  try {
    if (!mcp.isDynamic) {
      return res.status(400).json({
        error: 'Server management is only available in dynamic mode'
      });
    }

    const serverConfig = req.body;
    
    // Validate configuration
    const configLoader = new MCPConfigLoader();
    if (!configLoader.validateServerConfig(serverConfig)) {
      return res.status(400).json({
        error: 'Invalid server configuration',
        message: 'Configuration does not match the required schema'
      });
    }

    const serverInfo = await mcp.addServer(serverConfig);
    
    res.status(201).json({
      message: 'Server added successfully',
      server: serverInfo,
      stats: mcp.getServerStats()
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to add server',
      message: error.message
    });
  }
});

/**
 * Remove a server (dynamic mode only)
 */
router.delete('/servers/:serverId', async (req, res) => {
  try {
    if (!mcp.isDynamic) {
      return res.status(400).json({
        error: 'Server management is only available in dynamic mode'
      });
    }

    const { serverId } = req.params;
    const success = await mcp.removeServer(serverId);
    
    if (success) {
      res.json({
        message: `Server '${serverId}' removed successfully`,
        stats: mcp.getServerStats()
      });
    } else {
      res.status(404).json({
        error: `Server '${serverId}' not found`
      });
    }
  } catch (error) {
    res.status(500).json({
      error: 'Failed to remove server',
      message: error.message
    });
  }
});

/**
 * Reload configuration (dynamic mode only)
 */
router.post('/reload', async (req, res) => {
  try {
    if (!mcp.isDynamic) {
      return res.status(400).json({
        error: 'Configuration reload is only available in dynamic mode'
      });
    }

    const { configPath } = req.body;
    const success = await mcp.reloadConfiguration(configPath);
    
    if (success) {
      res.json({
        message: 'Configuration reloaded successfully',
        stats: mcp.getServerStats(),
        config: mcp.getConfiguration()
      });
    } else {
      res.status(500).json({
        error: 'Failed to reload configuration'
      });
    }
  } catch (error) {
    res.status(500).json({
      error: 'Failed to reload configuration',
      message: error.message
    });
  }
});

/**
 * Get configuration schema
 */
router.get('/schema', (req, res) => {
  try {
    const schemaPath = path.join(__dirname, '../config/server-schema.json');
    const schema = JSON.parse(fs.readFileSync(schemaPath, 'utf8'));
    
    res.json({
      schema,
      examples: {
        basic: '/api/mcp/examples/basic',
        multiDatabase: '/api/mcp/examples/multi-database',
        externalApis: '/api/mcp/examples/external-apis'
      }
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to get schema',
      message: error.message
    });
  }
});

/**
 * Get example configurations
 */
router.get('/examples/:example', (req, res) => {
  try {
    const { example } = req.params;
    const { format } = req.query; // Optional format parameter

    let examplePath;
    if (example === 'cursor-format') {
      examplePath = path.join(__dirname, '../config/cursor-format.json');
    } else {
      examplePath = path.join(__dirname, '../config/examples', `${example}.json`);
    }

    if (!fs.existsSync(examplePath)) {
      return res.status(404).json({
        error: 'Example configuration not found',
        available: ['multi-database', 'external-apis', 'cursor-format']
      });
    }

    let exampleConfig = JSON.parse(fs.readFileSync(examplePath, 'utf8'));

    // Convert format if requested
    if (format === 'cursor' && example !== 'cursor-format') {
      const configLoader = new MCPConfigLoader();
      exampleConfig = configLoader.convertToCursorFormat(exampleConfig);
    }

    res.json({
      name: example,
      description: `Example configuration: ${example}`,
      format: format || (example === 'cursor-format' ? 'cursor' : 'enhanced'),
      config: exampleConfig
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to get example configuration',
      message: error.message
    });
  }
});

/**
 * Test tool call
 */
router.post('/test-tool', async (req, res) => {
  try {
    const { toolName, params, requiredCapabilities } = req.body;
    
    if (!toolName) {
      return res.status(400).json({
        error: 'Tool name is required'
      });
    }

    const result = await mcp.callTool(toolName, params || {}, requiredCapabilities || []);
    
    res.json({
      success: true,
      result,
      executedAt: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      error: 'Tool call failed',
      message: error.message,
      toolName,
      params
    });
  }
});

/**
 * Convert configuration between formats
 */
router.post('/convert', (req, res) => {
  try {
    const { config, fromFormat, toFormat } = req.body;

    if (!config || !fromFormat || !toFormat) {
      return res.status(400).json({
        error: 'Missing required parameters: config, fromFormat, toFormat'
      });
    }

    const configLoader = new MCPConfigLoader();
    let result;

    if (fromFormat === 'cursor' && toFormat === 'enhanced') {
      // Validate Cursor format first
      if (!configLoader.validateCursorFormat(config)) {
        return res.status(400).json({
          error: 'Invalid Cursor format configuration'
        });
      }
      result = configLoader.cursorAdapter.cursorToEnhanced(config);
    } else if (fromFormat === 'enhanced' && toFormat === 'cursor') {
      // Validate Enhanced format first
      if (!configLoader.validateConfig(config)) {
        return res.status(400).json({
          error: 'Invalid Enhanced format configuration'
        });
      }
      result = configLoader.convertToCursorFormat(config);
    } else {
      return res.status(400).json({
        error: 'Unsupported conversion',
        supportedConversions: [
          { from: 'cursor', to: 'enhanced' },
          { from: 'enhanced', to: 'cursor' }
        ]
      });
    }

    res.json({
      message: `Successfully converted from ${fromFormat} to ${toFormat}`,
      originalFormat: fromFormat,
      targetFormat: toFormat,
      result
    });
  } catch (error) {
    res.status(500).json({
      error: 'Conversion failed',
      message: error.message
    });
  }
});

/**
 * Load Cursor format configuration
 */
router.post('/load-cursor', async (req, res) => {
  try {
    if (!mcp.isDynamic) {
      return res.status(400).json({
        error: 'Cursor format loading is only available in dynamic mode'
      });
    }

    const { config } = req.body;

    if (!config || !config.mcpServers) {
      return res.status(400).json({
        error: 'Invalid Cursor format: missing mcpServers'
      });
    }

    const configLoader = new MCPConfigLoader();

    // Validate Cursor format
    if (!configLoader.validateCursorFormat(config)) {
      return res.status(400).json({
        error: 'Invalid Cursor format configuration'
      });
    }

    // Convert to Enhanced format
    const enhancedConfig = configLoader.cursorAdapter.cursorToEnhanced(config);

    // Apply the configuration (this would require extending the server manager)
    // For now, just return the converted configuration
    res.json({
      message: 'Cursor configuration processed successfully',
      originalServers: Object.keys(config.mcpServers).length,
      convertedServers: enhancedConfig.servers.length,
      enhancedConfig
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to load Cursor configuration',
      message: error.message
    });
  }
});

/**
 * Get supported formats
 */
router.get('/formats', (req, res) => {
  try {
    const configLoader = new MCPConfigLoader();
    const formats = configLoader.getSupportedFormats();

    res.json({
      supportedFormats: formats,
      currentMode: mcp.isDynamic ? 'dynamic' : 'legacy',
      recommendations: {
        'cursor': 'Use for compatibility with Cursor/Claude Desktop',
        'enhanced': 'Use for advanced features like load balancing, health monitoring, and failover'
      }
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to get supported formats',
      message: error.message
    });
  }
});

/**
 * Health check endpoint
 */
router.get('/health', (req, res) => {
  try {
    const isReady = mcp.isDynamic ? mcp.isReady() : (mcp.client?.isConnected || false);
    const stats = mcp.isDynamic ? mcp.getServerStats() : { totalServers: 1, activeServers: isReady ? 1 : 0 };

    res.json({
      status: isReady ? 'healthy' : 'unhealthy',
      mode: mcp.isDynamic ? 'dynamic' : 'legacy',
      isReady,
      stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

module.exports = router;
