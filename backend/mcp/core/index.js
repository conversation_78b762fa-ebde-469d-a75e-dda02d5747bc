/**
 * MCP Module - Main Entry Point
 * Provides organized access to all MCP functionality
 * Exports core, LLM, and tool services in a structured way
 */
const mcpClient = require('./client');
const mcpBridge = require('./bridge');
const llmService = require('../llm/service');

// Export the modules
module.exports = {
  client: mcpClient,
  bridge: mcpBridge,
  llmService: llmService,

  // Export key functions directly for convenience
  initialize: mcpBridge.initialize,
  close: mcpBridge.close,
  getTools: mcpBridge.getTools,
  callTool: mcpBridge.callTool,
  processQuery: mcpBridge.processQuery,
  getActiveLLM: mcpBridge.getActiveLLM,
  setActiveLLM: mcpBridge.setActiveLLM
};
