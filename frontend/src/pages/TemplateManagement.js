import React, { useState, useEffect } from 'react';
import {
  Container,
  Typo<PERSON>,
  Button,
  Paper,
  Grid,
  Box,
  IconButton,
  CircularProgress,
  Chip,
  TextField,
  Divider,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Snackbar,
  Alert,
  Tooltip,
  Card,
  CardContent,
  CardActions,
} from '@mui/material';
import {
  Delete,
  CloudUpload,
  Refresh,
  PlayCircleFilled,
  Info,
  Description,
  FileDownload,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import TemplateEditor from '../components/templates/TemplateEditor';
import { styled } from '@mui/system';

// Custom styled components can be added here

const API_HOST = process.env.REACT_APP_API_HOST || 'localhost';
const API_BASE_URL = `http://${API_HOST}:5000`;

const TemplateManagement = () => {
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [file, setFile] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openDeployDialog, setOpenDeployDialog] = useState(false);
  const [openTemplateEditor, setOpenTemplateEditor] = useState(false);
  const [templateContent, setTemplateContent] = useState(null);
  const [deploymentSettings, setDeploymentSettings] = useState({
    containerName: '',
    leshanIpAddress: API_HOST,
    mqttBrokerIp: API_HOST,
    mqttBrokerPort: '1883',
    mqttTopic: '',
    mqttUsername: '',
    mqttPassword: ''
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Fetch templates from the backend
  const fetchTemplates = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`${API_BASE_URL}/api/templates`);

      if (!response.ok) {
        let errorMessage = 'Failed to fetch templates';

        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;

          // Extract specific error contexts
          if (errorMessage.includes("ECONNREFUSED")) {
            errorMessage = "Connection refused: Could not connect to the backend server. Please check if the server is running.";
          } else if (errorMessage.includes("timeout")) {
            errorMessage = "Request timeout: The server took too long to respond. This might be due to network issues or high server load.";
          } else if (response.status === 404) {
            errorMessage = "API endpoint not found: The templates endpoint could not be found. Please check the server configuration.";
          } else if (response.status === 500) {
            errorMessage = "Server error: The server encountered an internal error. Please check the server logs for more details.";
          } else if (errorMessage.includes("Failed to read templates folder")) {
            errorMessage = "Template folder error: The server could not access the templates folder. Please check if the folder exists and has proper permissions.";
          }
        } catch (jsonError) {
          // If we can't parse the error as JSON, use the status text
          errorMessage = response.statusText || errorMessage;
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      setTemplates(data);
    } catch (error) {
      setError(error.message);
      showSnackbar(`Error: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTemplates();
  }, []);

  // Handle file upload
  const handleFileUpload = async () => {
    if (!file) return;
    const formData = new FormData();
    formData.append('file', file);

    try {
      setLoading(true); // Show loading indicator
      const response = await fetch(`${API_BASE_URL}/api/templates/upload`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        let errorMessage = 'Failed to upload template';

        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;

          // Extract specific error contexts
          if (errorMessage.includes("Invalid JSON")) {
            errorMessage = `Invalid JSON file: The file "${file.name}" is not a valid JSON file. Please check the file format.`;
          } else if (errorMessage.includes("already exists")) {
            errorMessage = `Template already exists: A template with the name "${file.name}" already exists. Please delete the existing template first or rename your file.`;
          } else if (errorMessage.includes("permission denied")) {
            errorMessage = `Permission denied: The server doesn't have permission to write to the templates directory. Please check the server configuration.`;
          } else if (errorMessage.includes("file too large")) {
            errorMessage = `File too large: The template file "${file.name}" exceeds the maximum allowed size.`;
          }
        } catch (jsonError) {
          // If we can't parse the error as JSON, use the status text
          errorMessage = response.statusText || errorMessage;
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      showSnackbar(`Template ${file.name} uploaded successfully`);
      setFile(null); // Reset file input
      fetchTemplates(); // Refresh template list automatically
    } catch (error) {
      showSnackbar(`Error: ${error.message}`, 'error');
      setError(error.message);
    } finally {
      setLoading(false); // Hide loading indicator
    }
  };

  // Show snackbar message
  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  // Open template info dialog
  const handleOpenDialog = (template) => {
    setSelectedTemplate(template);
    setOpenDialog(true);
  };

  // Close template info dialog
  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  // Open delete confirmation dialog
  const handleOpenDeleteDialog = (template) => {
    setSelectedTemplate(template);
    setOpenDeleteDialog(true);
  };

  // Close delete confirmation dialog
  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
  };

  // Open deployment dialog
  const handleOpenDeployDialog = (template) => {
    setSelectedTemplate(template);
    // Initialize with template name as default container name
    setDeploymentSettings({
      containerName: template.name.replace('.json', ''),
      leshanIpAddress: API_HOST,
      mqttBrokerIp: API_HOST,
      mqttBrokerPort: '1883',
      mqttTopic: '',
      mqttUsername: '',
      mqttPassword: ''
    });
    setOpenDeployDialog(true);
  };

  // Open template editor
  const handleOpenTemplateEditor = async (template) => {
    setSelectedTemplate(template);
    setLoading(true);

    try {
      // Fetch the template content
      const response = await fetch(`${API_BASE_URL}/api/templates/${template.name}/content`);

      if (!response.ok) {
        throw new Error(`Failed to fetch template content: ${response.statusText}`);
      }

      const data = await response.json();
      setTemplateContent(data);
      setOpenTemplateEditor(true);
    } catch (error) {
      console.error('Error fetching template content:', error);
      showSnackbar(`Error: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  // Close template editor
  const handleCloseTemplateEditor = () => {
    setOpenTemplateEditor(false);
  };

  // Close deployment dialog
  const handleCloseDeployDialog = () => {
    setOpenDeployDialog(false);
  };

  // Handle deployment settings change
  const handleDeploymentSettingChange = (e) => {
    const { name, value } = e.target;
    setDeploymentSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle Delete template
  const handleDeleteTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      setLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/templates/${selectedTemplate.name}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        let errorMessage = 'Failed to delete template';

        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;

          // Extract specific error contexts
          if (errorMessage.includes("not found")) {
            errorMessage = `Template not found: The template "${selectedTemplate.name}" may have already been deleted or doesn't exist.`;
          } else if (errorMessage.includes("permission denied")) {
            errorMessage = `Permission denied: You don't have sufficient permissions to delete the template "${selectedTemplate.name}".`;
          } else if (errorMessage.includes("is in use")) {
            errorMessage = `Template in use: The template "${selectedTemplate.name}" is currently in use by a deployed virtual object. Please delete the virtual object first.`;
          }
        } catch (jsonError) {
          // If we can't parse the error as JSON, use the status text
          errorMessage = response.statusText || errorMessage;
        }

        throw new Error(errorMessage);
      }

      showSnackbar(`Template ${selectedTemplate.name} deleted successfully`);
      setOpenDeleteDialog(false);
      fetchTemplates(); // Refresh template list
    } catch (error) {
      showSnackbar(`Error: ${error.message}`, 'error');
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle deployment from template editor
  const handleDeployFromEditor = async (payload, settings) => {
    try {
      setLoading(true);
      // Log the payload for debugging
      console.log('Deploying with payload:', payload);
      // Call the deployment API
      const response = await fetch(`${API_BASE_URL}/api/temp-deploy`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to deploy template');
      }
      const result = await response.json();
      showSnackbar(`Node-RED container "${payload.containerName}" deployed successfully`);
      handleCloseTemplateEditor();
    } catch (error) {
      console.error('Error deploying template:', error);
      showSnackbar(`Error: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleDeploy = async () => {
    if (!selectedTemplate || !deploymentSettings.containerName || !deploymentSettings.leshanIpAddress) {
      showSnackbar('Please provide container name and Leshan IP address', 'error');
      return;
    }

    // Validate container name - Docker container names must be valid DNS names
    if (!/^[a-zA-Z0-9][a-zA-Z0-9_.-]*$/.test(deploymentSettings.containerName)) {
      showSnackbar('Invalid container name. Container names must contain only letters, numbers, underscores, periods, or hyphens, and must start with a letter or number.', 'error');
      return;
    }

    // Validate MQTT broker IP if provided
    if (deploymentSettings.mqttBrokerIp && !/^(?:\d{1,3}\.){3}\d{1,3}$|^localhost$/.test(deploymentSettings.mqttBrokerIp)) {
      showSnackbar('Invalid MQTT broker IP address format', 'error');
      return;
    }

    // Validate MQTT broker port if provided
    if (deploymentSettings.mqttBrokerPort && !/^\d+$/.test(deploymentSettings.mqttBrokerPort)) {
      showSnackbar('MQTT broker port must be a number', 'error');
      return;
    }

    try {
      setLoading(true); // Show loading indicator
      // Add detailed logging for debugging
      console.log(`Deploying template: ${selectedTemplate.name} with settings:`, deploymentSettings);

      // Log the exact payload being sent to the backend
      const payload = {
        templateName: selectedTemplate.name,
        containerName: deploymentSettings.containerName,
        leshanIpAddress: deploymentSettings.leshanIpAddress,
        mqttBrokerIp: deploymentSettings.mqttBrokerIp,
        mqttBrokerPort: deploymentSettings.mqttBrokerPort,
        mqttTopic: deploymentSettings.mqttTopic,
        mqttUsername: deploymentSettings.mqttUsername,
        mqttPassword: deploymentSettings.mqttPassword
      };
      console.log('Sending payload to backend:', JSON.stringify(payload, null, 2));

      const response = await fetch(`${API_BASE_URL}/api/temp-deploy`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      // Log the response status for debugging
      console.log(`Deploy response status: ${response.status}`);

      const responseText = await response.text();
      console.log(`Deploy response text:`, responseText);

      // Try to parse the response as JSON
      let responseData;
      try {
        responseData = JSON.parse(responseText);
      } catch (e) {
        console.error('Error parsing response as JSON:', e);
        throw new Error(`Server returned invalid JSON: ${responseText.substring(0, 100)}...`);
      }

      if (!response.ok) {
        let errorMessage = responseData.message || 'Failed to deploy template';
        const errorDetail = responseData.error || '';

        console.error('Deploy error:', { message: errorMessage, detail: errorDetail });

        // Extract specific error contexts
        if (errorDetail.includes("already in use") || errorMessage.includes("already in use")) {
          // Container name conflict error
          errorMessage = `Container name conflict: A virtual object with name "${deploymentSettings.containerName}" already exists. Please choose a different name or delete the existing one first.`;
        } else if (errorMessage.includes("Template not found")) {
          errorMessage = `Template not found: The template "${selectedTemplate.name}" could not be found. It may have been deleted.`;
        } else if (errorDetail.includes("ECONNREFUSED") || errorMessage.includes("ECONNREFUSED")) {
          // Connection refused error
          errorMessage = `Connection refused: Could not connect to the Docker daemon. Please check if Docker is running.`;
        } else if (errorDetail.includes("timeout") || errorMessage.includes("timeout")) {
          // Timeout error
          errorMessage = `Operation timed out: The server took too long to respond. This might be due to network issues or high server load.`;
        } else if (errorDetail) {
          // If we have error details but no specific match, include them in the message
          errorMessage = `${errorMessage}: ${errorDetail}`;
        }

        throw new Error(errorMessage);
      }

      showSnackbar(`Node-RED container "${deploymentSettings.containerName}" deployed successfully`);
      handleCloseDeployDialog(); // Close the dialog after successful deployment
    } catch (error) {
      console.error('Deploy error (caught):', error);
      showSnackbar(`Error: ${error.message}`, 'error');
    } finally {
      setLoading(false); // Hide loading indicator
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper elevation={3} sx={{ p: 4, backgroundColor: '#f8f9fa' }}>
        <Typography variant="h4" gutterBottom>
          Template Management
        </Typography>

        {/* File Upload Section */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 3 }}>
          <Button
            variant="contained"
            color="primary"
            component="label"
            startIcon={<CloudUpload />}
          >
            Select Template
            <input
              type="file"
              hidden
              accept=".json"
              onChange={(e) => setFile(e.target.files[0])}
            />
          </Button>

          <Button
            variant="contained"
            color="primary"
            onClick={handleFileUpload}
            disabled={!file}
            startIcon={<CloudUpload />}
            sx={{ ml: 2 }}
          >
            Upload
          </Button>

          <Button
            variant="contained"
            color="secondary"
            onClick={fetchTemplates}
            startIcon={<Refresh />}
            sx={{ ml: 2 }}
          >
            Refresh
          </Button>
        </Box>

        {file && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="body1" color="text.secondary">
              Selected file: {file.name}
            </Typography>
          </Box>
        )}

        {/* Error Handling */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Typography color="error" sx={{ mb: 2 }}>
            Error: {error}
          </Typography>
        ) : (
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Templates List
            </Typography>

            {/* Template List as Cards */}
            <Grid container spacing={3}>
              {templates.map((template) => (
                <Grid item xs={12} sm={6} md={4} key={template.name}>
                  <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                    <CardContent sx={{ flexGrow: 1 }}>
                      <Typography variant="h6" gutterBottom>
                        {template.name.replace('.json', '')}
                      </Typography>
                      <Chip
                        label="Node-RED Flow"
                        color="primary"
                        variant="outlined"
                        size="small"
                        sx={{ mb: 2 }}
                      />
                      <Typography variant="body2" color="text.secondary">
                        Last modified: {new Date(template.lastModified).toLocaleString()}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Size: {Math.round(template.size / 1024)} KB
                      </Typography>
                    </CardContent>
                    <CardActions>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          onClick={() => handleOpenDialog(template)}
                        >
                          <Info />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit & Deploy Template">
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleOpenTemplateEditor(template)}
                        >
                          <PlayCircleFilled />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Template">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleOpenDeleteDialog(template)}
                        >
                          <Delete />
                        </IconButton>
                      </Tooltip>
                    </CardActions>
                  </Card>
                </Grid>
              ))}
            </Grid>

            {templates.length === 0 && !loading && (
              <Typography variant="body1" sx={{ textAlign: 'center', my: 4 }}>
                No templates found. Upload a template to get started.
              </Typography>
            )}
          </Box>
        )}
      </Paper>

      {/* Template Info Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md">
        <DialogTitle>
          Template Details: {selectedTemplate?.name}
        </DialogTitle>
        <DialogContent>
          <DialogContentText component="div">
            <Typography variant="body1" gutterBottom>
              <strong>Name:</strong> {selectedTemplate?.name}
            </Typography>
            <Typography variant="body1" gutterBottom>
              <strong>Size:</strong> {selectedTemplate ? Math.round(selectedTemplate.size / 1024) : 0} KB
            </Typography>
            <Typography variant="body1" gutterBottom>
              <strong>Last Modified:</strong> {selectedTemplate ? new Date(selectedTemplate.lastModified).toLocaleString() : ''}
            </Typography>
            <Divider sx={{ my: 2 }} />
            <Typography variant="body2" color="text.secondary">
              This template contains a Node-RED flow that can be deployed as a container.
            </Typography>
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Close</Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              handleCloseDialog();
              handleOpenTemplateEditor(selectedTemplate);
            }}
            startIcon={<PlayCircleFilled />}
          >
            Edit & Deploy
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>
        <DialogTitle>Confirm Deletion</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the template "{selectedTemplate?.name}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
          <Button onClick={handleDeleteTemplate} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Deployment Dialog */}
      <Dialog open={openDeployDialog} onClose={handleCloseDeployDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Deploy Template</DialogTitle>
        <DialogContent>
          <DialogContentText component="div" sx={{ mb: 2 }}>
            Configure deployment settings for template: <strong>{selectedTemplate?.name}</strong>
          </DialogContentText>

          <TextField
            fullWidth
            margin="normal"
            label="Container Name"
            name="containerName"
            value={deploymentSettings.containerName}
            onChange={handleDeploymentSettingChange}
            helperText="This name will be used for both the container and the Leshan endpoint"
            required
            error={deploymentSettings.containerName && !/^[a-zA-Z0-9][a-zA-Z0-9_.-]*$/.test(deploymentSettings.containerName)}
            FormHelperTextProps={{
              style: {
                display: 'block',
                marginTop: '8px'
              }
            }}
          />

          <TextField
            fullWidth
            margin="normal"
            label="Leshan IP Address"
            name="leshanIpAddress"
            value={deploymentSettings.leshanIpAddress}
            onChange={handleDeploymentSettingChange}
            helperText="IP address of the Leshan server"
            required
          />

          <Typography variant="h6" sx={{ mt: 3, mb: 1 }}>
            MQTT Configuration
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={8}>
              <TextField
                fullWidth
                margin="normal"
                label="MQTT Broker IP"
                name="mqttBrokerIp"
                value={deploymentSettings.mqttBrokerIp}
                onChange={handleDeploymentSettingChange}
                helperText="IP address of the MQTT broker"
              />
            </Grid>
            <Grid item xs={4}>
              <TextField
                fullWidth
                margin="normal"
                label="MQTT Broker Port"
                name="mqttBrokerPort"
                value={deploymentSettings.mqttBrokerPort}
                onChange={handleDeploymentSettingChange}
                helperText="Port of the MQTT broker"
              />
            </Grid>
          </Grid>

          <TextField
            fullWidth
            margin="normal"
            label="MQTT Topic"
            name="mqttTopic"
            value={deploymentSettings.mqttTopic}
            onChange={handleDeploymentSettingChange}
            helperText="Topic to subscribe to (leave empty to use default from template)"
          />

          <Grid container spacing={2}>
            <Grid item xs={6}>
              <TextField
                fullWidth
                margin="normal"
                label="MQTT Username"
                name="mqttUsername"
                value={deploymentSettings.mqttUsername}
                onChange={handleDeploymentSettingChange}
                helperText="Username for MQTT authentication (optional)"
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                margin="normal"
                label="MQTT Password"
                name="mqttPassword"
                type="password"
                value={deploymentSettings.mqttPassword}
                onChange={handleDeploymentSettingChange}
                helperText="Password for MQTT authentication (optional)"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeployDialog}>Cancel</Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleDeploy}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <PlayCircleFilled />}
          >
            {loading ? 'Deploying...' : 'Deploy'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Template Editor Dialog */}
      <TemplateEditor
        open={openTemplateEditor}
        handleClose={handleCloseTemplateEditor}
        selectedTemplate={selectedTemplate}
        templateContent={templateContent}
        handleDeploy={handleDeployFromEditor}
        loading={loading}
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default TemplateManagement;
