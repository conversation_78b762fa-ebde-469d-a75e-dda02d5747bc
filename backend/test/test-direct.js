/**
 * Test script to check InfluxDB token directly
 */
const http = require('http');
const url = new URL('/api/v2/query?org=ICT', 'http://localhost:8086');

// Use the token directly
const token = 'Pb3x9HQY-VOLWGaZAlrif5ThJDJ6oSpJvUSJtD62NY3EC5pXKDdpYmJer1X4yt1C5T_aYi8MgEcKByqP1rPjFQ==';

console.log('Testing InfluxDB connection with token:', token);

const options = {
  method: 'POST',
  headers: {
    'Authorization': `Token ${token}`,
    'Content-Type': 'application/vnd.flux',
    'Accept': 'application/csv'
  }
};

const req = http.request(url, options, (res) => {
  console.log(`Response status: ${res.statusCode}`);
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  res.on('end', () => {
    console.log('Response data:', data.substring(0, 200) + (data.length > 200 ? '...' : ''));
  });
});

req.on('error', (e) => {
  console.error('Request error:', e.message);
});

req.write('buckets()');
req.end();
