/**
 * AG-UI Error Boundary Component
 * Catches React errors and displays them in AG-UI compatible format
 */

import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Alert,
  Button,
  Chip,
  Divider
} from '@mui/material';
import {
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  BugReport as BugIcon
} from '@mui/icons-material';

class AGUIErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { 
      hasError: true,
      errorId: `agui-error-${Date.now()}`
    };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error for debugging
    console.error('AG-UI Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // You can also log the error to an error reporting service here
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null
    });
  };

  render() {
    if (this.state.hasError) {
      return (
        <Paper 
          elevation={3} 
          sx={{ 
            p: 3, 
            m: 2, 
            border: '2px solid',
            borderColor: 'error.main',
            backgroundColor: 'error.light',
            color: 'error.contrastText'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <ErrorIcon sx={{ mr: 2, fontSize: 32 }} />
            <Box>
              <Typography variant="h6" gutterBottom>
                AG-UI Component Error
              </Typography>
              <Chip 
                icon={<BugIcon />}
                label={`Error ID: ${this.state.errorId}`}
                size="small"
                color="error"
                variant="outlined"
              />
            </Box>
          </Box>

          <Alert severity="error" sx={{ mb: 2 }}>
            <Typography variant="body1" gutterBottom>
              <strong>Error:</strong> {this.state.error?.message || 'Unknown error occurred'}
            </Typography>
            {this.state.error?.name && (
              <Typography variant="body2" color="text.secondary">
                <strong>Type:</strong> {this.state.error.name}
              </Typography>
            )}
          </Alert>

          {this.props.showDetails && this.state.errorInfo && (
            <Box sx={{ mt: 2 }}>
              <Divider sx={{ mb: 2 }} />
              <Typography variant="subtitle2" gutterBottom>
                Error Details:
              </Typography>
              <Paper 
                variant="outlined" 
                sx={{ 
                  p: 2, 
                  backgroundColor: 'grey.100',
                  maxHeight: 200,
                  overflow: 'auto'
                }}
              >
                <Typography 
                  variant="body2" 
                  component="pre" 
                  sx={{ 
                    fontFamily: 'monospace',
                    fontSize: '0.75rem',
                    whiteSpace: 'pre-wrap',
                    color: 'text.primary'
                  }}
                >
                  {this.state.error?.stack || 'No stack trace available'}
                </Typography>
              </Paper>
            </Box>
          )}

          <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<RefreshIcon />}
              onClick={this.handleRetry}
            >
              Retry
            </Button>
            
            {this.props.onReport && (
              <Button
                variant="outlined"
                color="secondary"
                startIcon={<BugIcon />}
                onClick={() => this.props.onReport(this.state.error, this.state.errorInfo)}
              >
                Report Issue
              </Button>
            )}
          </Box>

          <Box sx={{ mt: 2 }}>
            <Typography variant="caption" color="text.secondary">
              This error was caught by the AG-UI Error Boundary. The component will be restored when you click Retry.
            </Typography>
          </Box>
        </Paper>
      );
    }

    return this.props.children;
  }
}

export default AGUIErrorBoundary;
