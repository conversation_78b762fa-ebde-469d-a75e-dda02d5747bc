#!/usr/bin/env node

/**
 * Test script for Cursor format support
 */

const CursorAdapter = require('./mcp/config/cursor-adapter');
const MCPConfigLoader = require('./mcp/config/config-loader');

async function testCursorFormat() {
  console.log('🧪 Testing Cursor Format Support\n');

  try {
    // Initialize adapter
    const adapter = new CursorAdapter();
    const configLoader = new MCPConfigLoader();

    // Test Cursor configuration
    const cursorConfig = {
      "mcpServers": {
        "influxdb": {
          "command": "npx",
          "args": ["influxdb-mcp-server"],
          "env": {
            "INFLUXDB_TOKEN": "test-token",
            "INFLUXDB_ORG": "test-org",
            "INFLUXDB_URL": "http://localhost:8086"
          }
        },
        "context7": {
          "command": "npx",
          "args": ["-y", "@upstash/context7-mcp@latest"]
        },
        "filesystem": {
          "command": "npx",
          "args": ["-y", "@modelcontextprotocol/server-filesystem@latest"],
          "env": {
            "FILESYSTEM_ALLOWED_DIRECTORIES": "/home/<USER>/projects"
          }
        }
      }
    };

    console.log('📋 Original Cursor Configuration:');
    console.log(`- Servers: ${Object.keys(cursorConfig.mcpServers).length}`);
    console.log(`- Server IDs: ${Object.keys(cursorConfig.mcpServers).join(', ')}`);

    // Test validation
    console.log('\n🔍 Validating Cursor format...');
    const isValid = configLoader.validateCursorFormat(cursorConfig);
    console.log(`✅ Validation result: ${isValid ? 'VALID' : 'INVALID'}`);

    if (!isValid) {
      console.log('❌ Cursor format validation failed');
      return;
    }

    // Test conversion
    console.log('\n🔄 Converting Cursor format to Enhanced format...');
    const enhancedConfig = adapter.cursorToEnhanced(cursorConfig);
    
    console.log('✅ Conversion successful!');
    console.log('\n📊 Enhanced Configuration Results:');
    console.log(`- Total servers: ${enhancedConfig.servers.length}`);
    console.log(`- Enabled servers: ${enhancedConfig.servers.filter(s => s.enabled).length}`);
    console.log(`- Load balancing: ${enhancedConfig.globalSettings.loadBalancing}`);
    
    console.log('\n🏷️  Server Details:');
    enhancedConfig.servers.forEach(server => {
      console.log(`  • ${server.name} (${server.id})`);
      console.log(`    Priority: ${server.metadata.priority}`);
      console.log(`    Capabilities: ${server.capabilities.join(', ')}`);
      console.log(`    Command: ${server.connection.command} ${server.connection.args.join(' ')}`);
      console.log('');
    });

    // Test reverse conversion
    console.log('🔄 Converting back to Cursor format...');
    const backToCursor = adapter.enhancedToCursor(enhancedConfig);
    
    console.log('✅ Reverse conversion successful!');
    console.log(`- Converted servers: ${Object.keys(backToCursor.mcpServers).length}`);
    console.log(`- Server IDs: ${Object.keys(backToCursor.mcpServers).join(', ')}`);

    // Test format detection
    console.log('\n🔍 Testing format detection...');
    const detectedFormat = configLoader.detectAndConvertFormat(cursorConfig);
    console.log('✅ Format detection successful!');
    console.log(`- Detected as Enhanced format with ${detectedFormat.servers.length} servers`);

    console.log('\n🎉 All tests passed! Cursor format support is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Run tests
if (require.main === module) {
  testCursorFormat();
}

module.exports = { testCursorFormat };
