# Enhanced MCP Architecture Documentation

## Overview

The Enhanced MCP (Model Context Protocol) Architecture provides a JSON-configuration-based system for dynamically managing multiple MCP servers. This architecture supports unlimited MCP servers without code changes, following modern AI agent framework patterns.

## Architecture Components

### 1. Configuration System
- **JSON Schema Validation**: Comprehensive schema for server definitions
- **Environment Variable Substitution**: Dynamic configuration with `${VAR_NAME}` syntax
- **Multiple Configuration Files**: Support for loading and merging multiple configs

### 2. Dynamic Server Manager
- **Multi-Transport Support**: STDIO, HTTP, WebSocket, TCP (STDIO implemented)
- **Health Monitoring**: Automatic health checks and reconnection
- **Load Balancing**: Round-robin, priority-based, least-loaded strategies
- **Failover**: Automatic failover to backup servers

### 3. Dual Bridge System
- **Legacy Bridge**: Backward compatibility with existing single-server setup
- **Dynamic Bridge**: New JSON-configuration-based multi-server support
- **Automatic Selection**: Environment-based bridge selection

## Quick Start

### Enable Dynamic Mode

Set environment variables:
```bash
# Enable dynamic mode
export USE_DYNAMIC_MCP=true

# Optional: Custom config path
export MCP_CONFIG_PATH=/path/to/your/config.json
```

### Basic Configuration

Create `backend/mcp/config/servers.json`:
```json
{
  "globalSettings": {
    "loadBalancing": "priority",
    "failover": true,
    "healthMonitoring": true
  },
  "servers": [
    {
      "id": "influxdb-primary",
      "name": "InfluxDB Primary Server",
      "enabled": true,
      "connection": {
        "type": "stdio",
        "command": "node",
        "args": ["${MCP_SERVER_PATH}"]
      },
      "authentication": {
        "envVars": [
          {"name": "INFLUXDB_TOKEN", "fromEnv": "INFLUXDB_TOKEN"},
          {"name": "INFLUXDB_ORG", "fromEnv": "INFLUXDB_ORG"}
        ]
      },
      "capabilities": ["time-series", "iot-data"],
      "metadata": {"priority": 10}
    }
  ]
}
```

## Configuration Schema

### Server Configuration
```json
{
  "id": "unique-server-id",
  "name": "Human Readable Name",
  "description": "Server description",
  "enabled": true,
  "connection": {
    "type": "stdio|http|websocket|tcp",
    "command": "node",
    "args": ["server.js"],
    "timeout": 30000,
    "retryAttempts": 3
  },
  "authentication": {
    "type": "none|token|basic|oauth",
    "envVars": [
      {"name": "API_KEY", "fromEnv": "MY_API_KEY"}
    ]
  },
  "capabilities": ["capability1", "capability2"],
  "metadata": {
    "priority": 10,
    "tags": ["tag1", "tag2"]
  },
  "healthCheck": {
    "enabled": true,
    "interval": 30000,
    "method": "listTools"
  }
}
```

## API Endpoints

### Configuration Management
- `GET /api/mcp/config` - Get current configuration
- `GET /api/mcp/stats` - Get server statistics
- `GET /api/mcp/tools` - Get available tools
- `POST /api/mcp/servers` - Add new server
- `DELETE /api/mcp/servers/:id` - Remove server
- `POST /api/mcp/reload` - Reload configuration

### Schema and Examples
- `GET /api/mcp/schema` - Get configuration schema
- `GET /api/mcp/examples/multi-database` - Multi-database example
- `GET /api/mcp/examples/external-apis` - External APIs example

### Testing
- `POST /api/mcp/test-tool` - Test tool execution
- `GET /api/mcp/health` - Health check

## Usage Examples

### Adding a Server Dynamically
```javascript
const serverConfig = {
  "id": "weather-api",
  "name": "Weather API Server",
  "enabled": true,
  "connection": {
    "type": "stdio",
    "command": "node",
    "args": ["weather-server.js"]
  },
  "capabilities": ["weather-data"],
  "metadata": {"priority": 5}
};

// Via API
fetch('/api/mcp/servers', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify(serverConfig)
});

// Via Code
const mcp = require('./mcp');
await mcp.addServer(serverConfig);
```

### Calling Tools
```javascript
// Call tool on best available server
const result = await mcp.callTool('query-data', {
  query: 'SELECT * FROM temperature',
  timeRange: '1h'
}, ['time-series']); // Required capabilities

console.log(`Tool executed on server: ${result.serverId}`);
```

### Load Balancing Strategies
```json
{
  "globalSettings": {
    "loadBalancing": "priority",    // Highest priority first
    "loadBalancing": "round-robin", // Rotate between servers
    "loadBalancing": "least-loaded" // Least queries first
  }
}
```

## Migration Guide

### From Legacy to Dynamic Mode

1. **Create Configuration File**:
   ```bash
   cp backend/mcp/config/servers.json my-config.json
   ```

2. **Update Environment Variables**:
   ```bash
   export USE_DYNAMIC_MCP=true
   export MCP_CONFIG_PATH=./my-config.json
   ```

3. **Test Configuration**:
   ```bash
   curl http://localhost:5000/api/mcp/health
   ```

### Backward Compatibility

The system automatically detects the mode:
- **Legacy Mode**: `USE_DYNAMIC_MCP=false` or not set
- **Dynamic Mode**: `USE_DYNAMIC_MCP=true` or `MCP_CONFIG_PATH` set

## Example Configurations

### Multi-Database Setup
```json
{
  "servers": [
    {
      "id": "influxdb-prod",
      "name": "Production InfluxDB",
      "metadata": {"priority": 10},
      "capabilities": ["time-series", "production"]
    },
    {
      "id": "influxdb-staging", 
      "name": "Staging InfluxDB",
      "metadata": {"priority": 5},
      "capabilities": ["time-series", "staging"]
    },
    {
      "id": "postgres-analytics",
      "name": "Analytics PostgreSQL",
      "metadata": {"priority": 7},
      "capabilities": ["analytics", "reporting"]
    }
  ]
}
```

### External APIs Integration
```json
{
  "servers": [
    {
      "id": "weather-service",
      "connection": {
        "type": "stdio",
        "command": "node",
        "args": ["weather-mcp-server.js"]
      },
      "authentication": {
        "envVars": [
          {"name": "WEATHER_API_KEY", "fromEnv": "OPENWEATHER_KEY"}
        ]
      },
      "capabilities": ["weather-data", "external-api"]
    }
  ]
}
```

## Best Practices

1. **Server Priorities**: Use priority 10 for primary, 5-7 for secondary servers
2. **Health Checks**: Enable for all production servers
3. **Capabilities**: Use descriptive capability tags for proper routing
4. **Environment Variables**: Use `fromEnv` for sensitive data
5. **Timeouts**: Set appropriate timeouts based on server response times
6. **Failover**: Always configure backup servers for critical capabilities

## Troubleshooting

### Common Issues

1. **Server Not Connecting**:
   - Check environment variables
   - Verify server path exists
   - Check authentication credentials

2. **Tool Not Found**:
   - Verify server provides the tool
   - Check capability requirements
   - Ensure server is connected

3. **Configuration Errors**:
   - Validate against schema: `GET /api/mcp/schema`
   - Check JSON syntax
   - Verify environment variable substitution

### Debug Mode
```bash
export MCP_LOG_LEVEL=debug
export USE_DYNAMIC_MCP=true
```

## Performance Considerations

- **Health Check Intervals**: Balance between responsiveness and overhead
- **Connection Timeouts**: Set based on network conditions
- **Load Balancing**: Choose strategy based on server capabilities
- **Tool Caching**: Results are not cached; implement if needed

## Security

- **Environment Variables**: Never hardcode sensitive data
- **Authentication**: Use appropriate auth methods for each server
- **Network Security**: Secure connections for remote servers
- **Access Control**: Implement API authentication if needed
