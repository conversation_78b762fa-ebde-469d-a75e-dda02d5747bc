FROM nodered/node-red:3.1.9-18

USER root

# Set npm cache to avoid permission errors
ENV NPM_CONFIG_CACHE=/tmp/npm-cache

# Set the correct working directory
WORKDIR /usr/src/node-red

# Install Leshan client node explicitly
RUN npm install --unsafe-perm node-red-contrib-lwm2m && \
    npm install --unsafe-perm node-red-contrib-influxdb && \
    npm install --node-red-contrib-traffic

#COPY entrypoint.sh /usr/src/node-red/entrypoint.sh
#RUN chmod +x /usr/src/node-red/entrypoint.sh
# Switch back to node-red user
USER node-red

# Specify the flows file to load
ENV FLOWS=flows.json

EXPOSE 1880

#docker build -t custom-nodered-image -f Dockerfile .
