/**
 * API functions for template management
 */

const API_HOST = process.env.REACT_APP_API_HOST || 'localhost';
const API_BASE_URL = `http://${API_HOST}:5000`;

/**
 * Fetch all templates
 * @returns {Promise<Array>} List of templates
 */
export const fetchTemplates = async () => {
  const response = await fetch(`${API_BASE_URL}/templates`);

  if (!response.ok) {
    let errorMessage = 'Failed to fetch templates';

    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorMessage;

      // Extract specific error contexts
      if (errorMessage.includes("ECONNREFUSED")) {
        errorMessage = "Connection refused: Could not connect to the backend server. Please check if the server is running.";
      } else if (errorMessage.includes("timeout")) {
        errorMessage = "Request timeout: The server took too long to respond. This might be due to network issues or high server load.";
      } else if (response.status === 404) {
        errorMessage = "API endpoint not found: The templates endpoint could not be found. Please check the server configuration.";
      } else if (response.status === 500) {
        errorMessage = "Server error: The server encountered an internal error. Please check the server logs for more details.";
      } else if (errorMessage.includes("Failed to read templates folder")) {
        errorMessage = "Template folder error: The server could not access the templates folder. Please check if the folder exists and has proper permissions.";
      }
    } catch (jsonError) {
      // If we can't parse the error as JSON, use the status text
      errorMessage = response.statusText || errorMessage;
    }

    throw new Error(errorMessage);
  }

  return await response.json();
};

/**
 * Upload a template file
 * @param {File} file - The template file to upload
 * @returns {Promise<Object>} Upload result
 */
export const uploadTemplate = async (file) => {
  if (!file) {
    throw new Error('No file provided');
  }

  const formData = new FormData();
  formData.append('file', file);

  console.log('Uploading file:', file.name);

  // Add more detailed error handling and logging
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

  const response = await fetch(`${API_BASE_URL}/templates/upload`, {
    method: 'POST',
    body: formData,
    signal: controller.signal,
    // Explicitly disable HTTP/2 to avoid protocol issues
    cache: 'no-cache',
    redirect: 'follow',
    referrerPolicy: 'no-referrer',
  });

  clearTimeout(timeoutId);

  console.log('Upload response status:', response.status);
  console.log('Upload response headers:', [...response.headers.entries()]);

  if (!response.ok) {
    let errorMessage = 'Failed to upload template';

    try {
      // First try to get the response as text
      const responseText = await response.text();
      console.log('Error response text:', responseText);

      // Then try to parse it as JSON if possible
      try {
        const errorData = JSON.parse(responseText);
        errorMessage = errorData.message || errorMessage;
      } catch (jsonParseError) {
        // If it's not valid JSON, use the text directly
        if (responseText) {
          errorMessage = responseText;
        }
      }

      // Extract specific error contexts
      if (errorMessage.includes("Invalid JSON")) {
        errorMessage = `Invalid JSON file: The file "${file.name}" is not a valid JSON file. Please check the file format.`;
      } else if (errorMessage.includes("already exists")) {
        errorMessage = `Template already exists: A template with the name "${file.name}" already exists. Please delete the existing template first or rename your file.`;
      } else if (errorMessage.includes("permission denied")) {
        errorMessage = `Permission denied: The server doesn't have permission to write to the templates directory. Please check the server configuration.`;
      } else if (errorMessage.includes("file too large")) {
        errorMessage = `File too large: The template file "${file.name}" exceeds the maximum allowed size.`;
      }
    } catch (responseError) {
      console.error('Error reading response:', responseError);
      // If we can't parse the error as JSON, use the status text
      errorMessage = response.statusText || errorMessage;
    }

    throw new Error(errorMessage);
  }

  // Try to parse the response as JSON, but handle text responses too
  try {
    const responseText = await response.text();
    console.log('Success response text:', responseText);
    return JSON.parse(responseText);
  } catch (parseError) {
    console.error('Error parsing response:', parseError);
    return { message: 'Template uploaded successfully, but response could not be parsed' };
  }
};

/**
 * Delete a template
 * @param {Object} template - The template to delete
 * @returns {Promise<Object>} Delete result
 */
export const deleteTemplate = async (template) => {
  if (!template) {
    throw new Error('No template provided');
  }

  const response = await fetch(`${API_BASE_URL}/templates/${template.name}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    let errorMessage = 'Failed to delete template';

    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorMessage;

      // Extract specific error contexts
      if (errorMessage.includes("not found")) {
        errorMessage = `Template not found: The template "${template.name}" may have already been deleted or doesn't exist.`;
      } else if (errorMessage.includes("permission denied")) {
        errorMessage = `Permission denied: You don't have sufficient permissions to delete the template "${template.name}".`;
      } else if (errorMessage.includes("is in use")) {
        errorMessage = `Template in use: The template "${template.name}" is currently in use by a deployed virtual object. Please delete the virtual object first.`;
      }
    } catch (jsonError) {
      // If we can't parse the error as JSON, use the status text
      errorMessage = response.statusText || errorMessage;
    }

    throw new Error(errorMessage);
  }

  return await response.json();
};

/**
 * Deploy a template
 * @param {Object} template - The template to deploy
 * @param {Object} deploymentSettings - Deployment settings
 * @returns {Promise<Object>} Deployment result
 */
export const deployTemplate = async (template, deploymentSettings) => {
  // Validate required parameters
  if (!template) {
    throw new Error('No template provided');
  }

  if (!deploymentSettings.containerName) {
    throw new Error('Container name is required');
  }

  if (!deploymentSettings.leshanIpAddress) {
    throw new Error('Leshan IP address is required');
  }

  if (!deploymentSettings.mqttBrokerIp) {
    throw new Error('MQTT broker IP is required');
  }

  if (!deploymentSettings.mqttPort) {
    throw new Error('MQTT port is required');
  }

  if (!deploymentSettings.mqttTopic) {
    throw new Error('MQTT topic is required');
  }

  // Validate container name - Docker container names must be valid DNS names
  if (!/^[a-zA-Z0-9][a-zA-Z0-9_.-]*$/.test(deploymentSettings.containerName)) {
    throw new Error('Invalid container name. Container names must contain only letters, numbers, underscores, periods, or hyphens, and must start with a letter or number.');
  }

  // Add detailed logging for debugging
  console.log(`Deploying template: ${template.name} with settings:`, deploymentSettings);

  // Log the exact payload being sent to the backend
  const payload = {
    templateName: template.name,
    containerName: deploymentSettings.containerName,
    leshanIpAddress: deploymentSettings.leshanIpAddress,
    mqttSettings: {
      brokerIp: deploymentSettings.mqttBrokerIp,
      port: deploymentSettings.mqttPort,
      topic: deploymentSettings.mqttTopic
    }
  };
  console.log('Sending payload to backend:', JSON.stringify(payload, null, 2));

  const response = await fetch(`${API_BASE_URL}/api/temp-deploy`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(payload),
  });

  // Log the response status for debugging
  console.log(`Deploy response status: ${response.status}`);

  const responseText = await response.text();
  console.log(`Deploy response text:`, responseText);

  // Try to parse the response as JSON
  let responseData;
  try {
    responseData = JSON.parse(responseText);
  } catch (e) {
    console.error('Error parsing response as JSON:', e);
    throw new Error(`Server returned invalid JSON: ${responseText.substring(0, 100)}...`);
  }

  if (!response.ok) {
    let errorMessage = responseData.message || 'Failed to deploy template';
    const errorDetail = responseData.error || '';

    console.error('Deploy error:', { message: errorMessage, detail: errorDetail });

    // Extract specific error contexts
    if (errorDetail.includes("already in use") || errorMessage.includes("already in use")) {
      // Container name conflict error
      errorMessage = `Container name conflict: A virtual object with name "${deploymentSettings.containerName}" already exists. Please choose a different name or delete the existing one first.`;
    } else if (errorMessage.includes("Template not found")) {
      errorMessage = `Template not found: The template "${template.name}" could not be found. It may have been deleted.`;
    } else if (errorDetail.includes("ECONNREFUSED") || errorMessage.includes("ECONNREFUSED")) {
      // Connection refused error
      errorMessage = `Connection refused: Could not connect to the Docker daemon. Please check if Docker is running.`;
    } else if (errorDetail.includes("timeout") || errorMessage.includes("timeout")) {
      // Timeout error
      errorMessage = `Operation timed out: The server took too long to respond. This might be due to network issues or high server load.`;
    } else if (errorDetail) {
      // If we have error details but no specific match, include them in the message
      errorMessage = `${errorMessage}: ${errorDetail}`;
    }

    throw new Error(errorMessage);
  }

  return responseData;
};
