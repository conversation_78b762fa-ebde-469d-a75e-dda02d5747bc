#!/usr/bin/env node

/**
 * Migration Script: Legacy to Dynamic MCP
 * Helps migrate from legacy single-server MCP to dynamic multi-server configuration
 */

const fs = require('fs');
const path = require('path');

class MCPMigrationTool {
  constructor() {
    this.configPath = path.join(__dirname, '../config/servers.json');
    this.examplePath = path.join(__dirname, '../config/examples');
  }

  /**
   * Generate configuration from current environment
   */
  generateConfigFromEnvironment() {
    const config = {
      globalSettings: {
        loadBalancing: 'priority',
        failover: true,
        healthMonitoring: true,
        logging: {
          level: 'info',
          enableMetrics: true
        }
      },
      servers: []
    };

    // Generate InfluxDB server config from environment
    if (process.env.MCP_SERVER_PATH && process.env.INFLUXDB_TOKEN) {
      const influxServer = {
        id: 'influxdb-migrated',
        name: 'Migrated InfluxDB Server',
        description: 'Automatically migrated from legacy configuration',
        enabled: true,
        connection: {
          type: 'stdio',
          command: 'node',
          args: ['${MCP_SERVER_PATH}'],
          timeout: 30000,
          retryAttempts: 3,
          retryDelay: 1000
        },
        authentication: {
          type: 'none',
          envVars: [
            {
              name: 'INFLUXDB_TOKEN',
              fromEnv: 'INFLUXDB_TOKEN'
            },
            {
              name: 'INFLUXDB_ORG',
              fromEnv: 'INFLUXDB_ORG'
            },
            {
              name: 'INFLUXDB_URL',
              fromEnv: 'INFLUXDB_URL'
            }
          ]
        },
        capabilities: [
          'time-series',
          'data-query',
          'data-write',
          'analytics',
          'iot-data'
        ],
        tools: [
          {
            name: 'query-data',
            description: 'Query time-series data from InfluxDB',
            category: 'data-access'
          },
          {
            name: 'write-data',
            description: 'Write data to InfluxDB',
            category: 'data-write'
          },
          {
            name: 'create-bucket',
            description: 'Create a new InfluxDB bucket',
            category: 'management'
          },
          {
            name: 'create-org',
            description: 'Create a new InfluxDB organization',
            category: 'management'
          }
        ],
        metadata: {
          version: '1.0.0',
          priority: 10,
          tags: ['database', 'time-series', 'iot', 'migrated'],
          maintainer: 'Migration Tool',
          documentation: 'https://docs.influxdata.com/'
        },
        healthCheck: {
          enabled: true,
          interval: 30000,
          timeout: 5000,
          method: 'listTools'
        }
      };

      config.servers.push(influxServer);
    }

    return config;
  }

  /**
   * Check current environment and suggest migration steps
   */
  analyzeEnvironment() {
    console.log('🔍 Analyzing current MCP environment...\n');

    const analysis = {
      hasLegacyConfig: false,
      hasDynamicConfig: false,
      environmentVars: {},
      recommendations: []
    };

    // Check for legacy environment variables
    const legacyVars = ['MCP_SERVER_PATH', 'INFLUXDB_TOKEN', 'INFLUXDB_ORG', 'INFLUXDB_URL'];
    legacyVars.forEach(varName => {
      if (process.env[varName]) {
        analysis.environmentVars[varName] = '✅ Set';
        analysis.hasLegacyConfig = true;
      } else {
        analysis.environmentVars[varName] = '❌ Not set';
      }
    });

    // Check for dynamic mode variables
    if (process.env.USE_DYNAMIC_MCP === 'true') {
      analysis.hasDynamicConfig = true;
    }

    if (process.env.MCP_CONFIG_PATH) {
      analysis.hasDynamicConfig = true;
      analysis.environmentVars.MCP_CONFIG_PATH = process.env.MCP_CONFIG_PATH;
    }

    // Check if config file exists
    const configExists = fs.existsSync(this.configPath);
    analysis.configFileExists = configExists;

    // Generate recommendations
    if (analysis.hasLegacyConfig && !analysis.hasDynamicConfig) {
      analysis.recommendations.push('✨ Ready for migration to dynamic mode');
      analysis.recommendations.push('📝 Generate configuration from current environment');
      analysis.recommendations.push('🔧 Set USE_DYNAMIC_MCP=true to enable dynamic mode');
    } else if (analysis.hasDynamicConfig) {
      analysis.recommendations.push('✅ Already using dynamic mode');
    } else {
      analysis.recommendations.push('⚠️  No MCP configuration detected');
      analysis.recommendations.push('📖 Check the documentation for setup instructions');
    }

    return analysis;
  }

  /**
   * Display analysis results
   */
  displayAnalysis(analysis) {
    console.log('📊 Environment Analysis Results:');
    console.log('================================\n');

    console.log('Environment Variables:');
    Object.entries(analysis.environmentVars).forEach(([key, value]) => {
      console.log(`  ${key}: ${value}`);
    });

    console.log(`\nConfiguration File: ${analysis.configFileExists ? '✅ Exists' : '❌ Not found'}`);
    console.log(`Dynamic Mode: ${analysis.hasDynamicConfig ? '✅ Enabled' : '❌ Disabled'}`);
    console.log(`Legacy Mode: ${analysis.hasLegacyConfig ? '✅ Detected' : '❌ Not detected'}\n`);

    console.log('Recommendations:');
    analysis.recommendations.forEach(rec => console.log(`  ${rec}`));
    console.log();
  }

  /**
   * Generate and save configuration file
   */
  async generateConfiguration(outputPath = null) {
    const config = this.generateConfigFromEnvironment();
    const filePath = outputPath || this.configPath;

    try {
      // Ensure directory exists
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // Write configuration
      fs.writeFileSync(filePath, JSON.stringify(config, null, 2));
      
      console.log(`✅ Configuration generated successfully!`);
      console.log(`📁 Saved to: ${filePath}`);
      console.log(`🔧 To enable dynamic mode, set: USE_DYNAMIC_MCP=true\n`);

      return true;
    } catch (error) {
      console.error(`❌ Failed to generate configuration: ${error.message}`);
      return false;
    }
  }

  /**
   * Display usage instructions
   */
  displayUsage() {
    console.log('🚀 MCP Migration Tool');
    console.log('====================\n');
    console.log('Usage:');
    console.log('  node migrate-to-dynamic.js [command] [options]\n');
    console.log('Commands:');
    console.log('  analyze     - Analyze current environment');
    console.log('  generate    - Generate configuration from environment');
    console.log('  examples    - List available example configurations');
    console.log('  help        - Show this help message\n');
    console.log('Options:');
    console.log('  --output    - Output path for generated configuration');
    console.log('  --example   - Copy example configuration\n');
    console.log('Examples:');
    console.log('  node migrate-to-dynamic.js analyze');
    console.log('  node migrate-to-dynamic.js generate');
    console.log('  node migrate-to-dynamic.js generate --output ./my-config.json');
    console.log('  node migrate-to-dynamic.js examples');
  }

  /**
   * List available example configurations
   */
  listExamples() {
    console.log('📚 Available Example Configurations:');
    console.log('===================================\n');

    try {
      const examples = fs.readdirSync(this.examplePath)
        .filter(file => file.endsWith('.json'))
        .map(file => file.replace('.json', ''));

      examples.forEach(example => {
        const examplePath = path.join(this.examplePath, `${example}.json`);
        const config = JSON.parse(fs.readFileSync(examplePath, 'utf8'));
        const serverCount = config.servers?.length || 0;
        
        console.log(`📄 ${example}`);
        console.log(`   Servers: ${serverCount}`);
        console.log(`   Path: ${examplePath}\n`);
      });

      console.log('To copy an example:');
      console.log('  cp backend/mcp/config/examples/[example].json backend/mcp/config/servers.json');
    } catch (error) {
      console.error(`❌ Failed to list examples: ${error.message}`);
    }
  }

  /**
   * Main execution function
   */
  async run() {
    const args = process.argv.slice(2);
    const command = args[0] || 'help';

    switch (command) {
      case 'analyze':
        const analysis = this.analyzeEnvironment();
        this.displayAnalysis(analysis);
        break;

      case 'generate':
        const outputIndex = args.indexOf('--output');
        const outputPath = outputIndex !== -1 ? args[outputIndex + 1] : null;
        await this.generateConfiguration(outputPath);
        break;

      case 'examples':
        this.listExamples();
        break;

      case 'help':
      default:
        this.displayUsage();
        break;
    }
  }
}

// Run the migration tool
if (require.main === module) {
  const migrationTool = new MCPMigrationTool();
  migrationTool.run().catch(error => {
    console.error('❌ Migration tool error:', error.message);
    process.exit(1);
  });
}

module.exports = MCPMigrationTool;
