{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@anthropic-ai/sdk": "^0.40.1", "@google/generative-ai": "^0.24.1", "@modelcontextprotocol/sdk": "^1.10.2", "ajv": "^8.12.0", "axios": "^1.6.7", "body-parser": "^1.20.3", "cors": "^2.8.5", "dockerode": "^4.0.4", "dotenv": "^16.5.0", "express": "^4.21.2", "moment": "^2.30.1", "multer": "^1.4.5-lts.1", "openai": "^4.96.2", "tar": "^7.4.3"}}