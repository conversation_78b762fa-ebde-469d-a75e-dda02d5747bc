/**
 * MCP Tools Panel Component
 * Displays available MCP tools and allows direct execution
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Alert,
  CircularProgress,
  Tabs,
  Tab
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  PlayArrow as PlayIcon,
  Storage as DatabaseIcon,
  Folder as FilesystemIcon,
  Analytics as AnalyticsIcon,
  Settings as ManagementIcon,
  Extension as OtherIcon
} from '@mui/icons-material';
import MCPToolsService from '../../services/MCPToolsService';

const MCPToolsPanel = ({ onToolExecute }) => {
  const [tools, setTools] = useState([]);
  const [toolCategories, setToolCategories] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedTool, setSelectedTool] = useState(null);
  const [toolParams, setToolParams] = useState('{}');
  const [executeDialog, setExecuteDialog] = useState(false);
  const [executing, setExecuting] = useState(false);
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    loadTools();
  }, []);

  const loadTools = async () => {
    try {
      setLoading(true);
      await MCPToolsService.loadTools();
      setTools(MCPToolsService.getAllTools());
      setToolCategories(MCPToolsService.toolCategories);
      setError(null);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleToolExecute = async () => {
    if (!selectedTool) return;

    try {
      setExecuting(true);
      let params = {};
      
      try {
        params = JSON.parse(toolParams);
      } catch (e) {
        throw new Error('Invalid JSON parameters');
      }

      const result = await MCPToolsService.executeTool(selectedTool.name, params);
      
      if (onToolExecute) {
        onToolExecute({
          tool: selectedTool,
          params,
          result
        });
      }

      setExecuteDialog(false);
      setSelectedTool(null);
      setToolParams('{}');
    } catch (err) {
      setError(err.message);
    } finally {
      setExecuting(false);
    }
  };

  const openExecuteDialog = (tool) => {
    setSelectedTool(tool);
    setToolParams(JSON.stringify(getToolExample(tool.name), null, 2));
    setExecuteDialog(true);
  };

  const getToolExample = (toolName) => {
    const examples = MCPToolsService.getToolExamples();
    
    // Find example in any category
    for (const category of Object.values(examples)) {
      if (category[toolName]) {
        return category[toolName].example;
      }
    }
    
    return {};
  };

  const getCategoryIcon = (category) => {
    const icons = {
      database: <DatabaseIcon />,
      filesystem: <FilesystemIcon />,
      analytics: <AnalyticsIcon />,
      management: <ManagementIcon />,
      other: <OtherIcon />
    };
    return icons[category] || <OtherIcon />;
  };

  const getCategoryColor = (category) => {
    const colors = {
      database: 'primary',
      filesystem: 'secondary',
      analytics: 'success',
      management: 'warning',
      other: 'default'
    };
    return colors[category] || 'default';
  };

  const categories = Object.keys(toolCategories);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" p={3}>
        <CircularProgress />
        <Typography variant="body2" sx={{ ml: 2 }}>
          Loading MCP tools...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Error loading tools: {error}
        <Button onClick={loadTools} size="small" sx={{ ml: 2 }}>
          Retry
        </Button>
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Available MCP Tools ({tools.length})
      </Typography>

      <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)} sx={{ mb: 2 }}>
        <Tab label="All Tools" />
        <Tab label="By Category" />
        <Tab label="Examples" />
      </Tabs>

      {activeTab === 0 && (
        <Grid container spacing={2}>
          {tools.map((tool, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Card variant="outlined" sx={{ height: '100%' }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    {tool.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    {tool.description || 'No description available'}
                  </Typography>
                  <Chip 
                    label={MCPToolsService.getToolCategory(tool.name)}
                    color={getCategoryColor(MCPToolsService.getToolCategory(tool.name))}
                    size="small"
                    sx={{ mb: 1 }}
                  />
                  <Box>
                    <Button
                      size="small"
                      startIcon={<PlayIcon />}
                      onClick={() => openExecuteDialog(tool)}
                    >
                      Execute
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {activeTab === 1 && (
        <Box>
          {categories.map((category) => (
            <Accordion key={category} sx={{ mb: 1 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box display="flex" alignItems="center">
                  {getCategoryIcon(category)}
                  <Typography sx={{ ml: 1, textTransform: 'capitalize' }}>
                    {category} ({toolCategories[category].length})
                  </Typography>
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  {toolCategories[category].map((tool, index) => (
                    <Grid item xs={12} sm={6} key={index}>
                      <Card variant="outlined">
                        <CardContent>
                          <Typography variant="subtitle2">{tool.name}</Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                            {tool.description}
                          </Typography>
                          <Button
                            size="small"
                            startIcon={<PlayIcon />}
                            onClick={() => openExecuteDialog(tool)}
                          >
                            Execute
                          </Button>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </AccordionDetails>
            </Accordion>
          ))}
        </Box>
      )}

      {activeTab === 2 && (
        <Box>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Common usage examples for MCP tools:
          </Typography>
          
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography>Database Tools Examples</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap' }}>
{`Query sensor data:
{
  "query": "SELECT * FROM temperature WHERE time > now() - 1h",
  "timeRange": "1h"
}

Write sensor data:
{
  "bucket": "sensors",
  "data": "temperature,location=room1 value=23.5"
}`}
              </Typography>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography>Filesystem Tools Examples</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap' }}>
{`Read file:
{
  "path": "/home/<USER>/projects/config.json"
}

Write file:
{
  "path": "/home/<USER>/projects/output.txt",
  "content": "Hello, World!"
}

List directory:
{
  "path": "/home/<USER>/projects"
}`}
              </Typography>
            </AccordionDetails>
          </Accordion>
        </Box>
      )}

      {/* Execute Tool Dialog */}
      <Dialog open={executeDialog} onClose={() => setExecuteDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Execute Tool: {selectedTool?.name}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {selectedTool?.description}
          </Typography>
          <TextField
            label="Parameters (JSON)"
            multiline
            rows={8}
            fullWidth
            value={toolParams}
            onChange={(e) => setToolParams(e.target.value)}
            variant="outlined"
            sx={{ fontFamily: 'monospace' }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setExecuteDialog(false)}>Cancel</Button>
          <Button 
            onClick={handleToolExecute} 
            variant="contained"
            disabled={executing}
            startIcon={executing ? <CircularProgress size={16} /> : <PlayIcon />}
          >
            {executing ? 'Executing...' : 'Execute'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MCPToolsPanel;
