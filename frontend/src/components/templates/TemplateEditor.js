import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  TextField,
  CircularProgress,
  Typography,
  Box,
  Paper,
  Grid,
  MenuItem,
  Dialog as MuiDialog,
  DialogTitle as MuiDialogTitle,
  <PERSON>alog<PERSON>ontent as MuiDialogContent,
  DialogActions as <PERSON>iDialogActions,
  Stack
} from '@mui/material';
import {
  PlayCircleFilled,
  Add as AddIcon
} from '@mui/icons-material';

// Add SENSOR_TYPES at the top-level so it is available everywhere
const SENSOR_TYPES = [
  { label: "Temperature", objectId: "3303", key: "5700", type: "Sensor" },
  { label: "Humidity", objectId: "3304", key: "5700", type: "Sensor" },
  { label: "Light", objectId: "3301", key: "5700", type: "Sensor" },
  { label: "Pressure", objectId: "3323", key: "5700", type: "Sensor" },
  { label: "Actuator", objectId: "3306", key: "5850", type: "Actuator" },
  { label: "Accelerometer X", objectId: "3313", key: "5702", type: "Sensor" },
  { label: "Accelerometer Y", objectId: "3313", key: "5703", type: "Sensor" },
  { label: "Accelerometer Z", objectId: "3313", key: "5704", type: "Sensor" },
  { label: "Gyroscope X", objectId: "3334", key: "5702", type: "Sensor" },
  { label: "Gyroscope Y", objectId: "3334", key: "5703", type: "Sensor" },
  { label: "Gyroscope Z", objectId: "3334", key: "5704", type: "Sensor" },
  { label: "Magnetometer X", objectId: "3314", key: "5702", type: "Sensor" },
  { label: "Magnetometer Y", objectId: "3314", key: "5703", type: "Sensor" },
  { label: "Magnetometer Z", objectId: "3314", key: "5704", type: "Sensor" },
  { label: "CO2", objectId: "3325", key: "5700", type: "Sensor" },
  { label: "Voltage", objectId: "3315", key: "5700", type: "Sensor" },
  { label: "Current", objectId: "3316", key: "5700", type: "Sensor" },
  { label: "Power", objectId: "3317", key: "5700", type: "Sensor" },
  { label: "Battery", objectId: "3", key: "9", type: "Sensor" },
  { label: "Location Latitude", objectId: "6", key: "0", type: "Sensor" },
  { label: "Location Longitude", objectId: "6", key: "1", type: "Sensor" },
  { label: "Location Altitude", objectId: "6", key: "2", type: "Sensor" },
  { label: "Location Uncertainty", objectId: "6", key: "3", type: "Sensor" },
  { label: "Location Velocity", objectId: "6", key: "4", type: "Sensor" },
  { label: "Location Timestamp", objectId: "6", key: "5", type: "Sensor" },
  { label: "Location Speed", objectId: "6", key: "6", type: "Sensor" },
  { label: "Location Direction", objectId: "6", key: "7", type: "Sensor" },
  { label: "Custom", objectId: "", key: "", type: "Custom" }
];

// Add this constant at the top of the file, after imports
const SENSOR_PROCESS_FUNCTION_CODE = `let sensorMappingsJson = env.get("SENSOR_MAPPINGS") || "{}";
let sensorMappings = {};
try {
    sensorMappings = JSON.parse(sensorMappingsJson);
} catch(e) {
    node.error("Failed to parse SENSOR_MAPPINGS environment variable: " + e.message);
    sensorMappings = {};
}
let incoming = msg.payload;
let topic = msg.topic; // This is the MQTT topic
let mapping = sensorMappings[topic] || {};
let fullLeshanUri = mapping.leshanUri || "Unknown/0/5700";
msg.payload = incoming || null;
msg.mqttTopic = topic; // Preserve the original MQTT topic
msg.topic = "/" + fullLeshanUri;
msg.deviceName = mapping.deviceName || topic;
return msg;`;

/**
 * Component for editing a template before deployment
 */
const TemplateEditor = ({
  open,
  handleClose,
  selectedTemplate,
  templateContent,
  handleDeploy,
  loading
}) => {
  // State for deployment settings
  const [deploymentSettings, setDeploymentSettings] = useState({
    containerName: '',
    leshanIpAddress: '**********'
  });
  // State for MQTT broker settings
  const [mqttBrokerSettings, setMqttBrokerSettings] = useState({
    brokerIp: '*************',
    port: 1883
  });
  // State for MQTT input nodes
  const [sensors, setSensors] = useState([]);
  // State for validation errors
  const [errors, setErrors] = useState({});
  // State for sensor type selection dialog
  const [pendingSensor, setPendingSensor] = useState(null);

  // Add/Edit Sensor Dialog State
  const [sensorDialogOpen, setSensorDialogOpen] = useState(false);
  const [sensorDialogEdit, setSensorDialogEdit] = useState(false);
  const [sensorDialogIndex, setSensorDialogIndex] = useState(null);

  // Initialize the editor when the template content changes
  useEffect(() => {
    if (templateContent) {
      // Parse the template content
      try {
        // Initialize deployment settings
        const containerName = selectedTemplate?.name.replace('.json', '') || '';
        setDeploymentSettings({
          containerName: containerName,
          leshanIpAddress: '**********'
        });

        // Find MQTT broker configuration
        const mqttBrokerConfig = templateContent.find(node =>
          node.type === 'mqtt-broker' ||
          (node.id && node.id.includes('mqttBrokerConfig'))
        );

        if (mqttBrokerConfig) {
          setMqttBrokerSettings({
            brokerIp: mqttBrokerConfig.broker || '*************',
            port: mqttBrokerConfig.port || 1883
          });
        }

        // Find LwM2M client node to extract topic information
        const lwm2mClientNode = templateContent.find(node => node.type === 'lwm2m client');
        let topicToResourceMap = {};

        // If we have a LwM2M client node with objects, extract the topic values from resource 9999
        if (lwm2mClientNode && lwm2mClientNode.objects) {
          // Parse the objects JSON if it's a string
          const objects = typeof lwm2mClientNode.objects === 'string'
            ? JSON.parse(lwm2mClientNode.objects)
            : lwm2mClientNode.objects;

          // Iterate through all object types (3303, 3304, etc.)
          Object.keys(objects).forEach(objectType => {
            // Iterate through all instances of this object type
            Object.keys(objects[objectType]).forEach(instanceId => {
              // Check if this instance has resource 9999
              if (objects[objectType][instanceId]['9999']) {
                // Store the topic value
                const topic = objects[objectType][instanceId]['9999'].value;
                topicToResourceMap[topic] = {
                  objectType,
                  instanceId
                };
                console.log(`Found LwM2M resource for topic ${topic}: ${objectType}/${instanceId}/9999`);
              }
            });
          });
        }

        // Find all MQTT input nodes
        const mqttNodes = templateContent.filter(node => node.type === 'mqtt in');

        // Create a more detailed mapping between MQTT nodes and LwM2M resources
        // This will help us track which specific LwM2M resource corresponds to which MQTT node
        const mqttNodeToLwM2MResourceMap = {};

        // If we have LwM2M resources, try to match them with MQTT nodes
        if (lwm2mClientNode && lwm2mClientNode.objects) {
          const objects = typeof lwm2mClientNode.objects === 'string'
            ? JSON.parse(lwm2mClientNode.objects)
            : lwm2mClientNode.objects;

          // First, create a map of all MQTT nodes by topic for easy lookup
          const mqttNodesByTopic = {};
          mqttNodes.forEach(node => {
            const topic = node.topic || '';
            if (topic) {
              if (!mqttNodesByTopic[topic]) {
                mqttNodesByTopic[topic] = [];
              }
              mqttNodesByTopic[topic].push(node);
            }
          });

          // Now, for each LwM2M resource with a 9999 value, find the corresponding MQTT node
          Object.keys(objects).forEach(objectType => {
            Object.keys(objects[objectType]).forEach(instanceId => {
              if (objects[objectType][instanceId]['9999']) {
                const topic = objects[objectType][instanceId]['9999'].value;

                // Find MQTT nodes with this topic
                const matchingNodes = mqttNodesByTopic[topic] || [];

                if (matchingNodes.length > 0) {
                  // If we have matching nodes, assign this resource to the first one
                  // that doesn't already have a resource assigned
                  for (const node of matchingNodes) {
                    if (!mqttNodeToLwM2MResourceMap[node.id]) {
                      mqttNodeToLwM2MResourceMap[node.id] = {
                        objectType,
                        instanceId,
                        topic
                      };
                      console.log(`Mapped MQTT node ${node.id} (${node.name}) with topic ${topic} to LwM2M resource ${objectType}/${instanceId}/9999`);
                      break;
                    }
                  }
                }
              }
            });
          });
        }

        // Find comment node with mapping (e.g., temp==temperature) and build topic->leshanName map
        const commentNode = templateContent.find(node => node.type === 'comment' && typeof node.info === 'string' && node.info.includes('=='));
        let topicToLeshanName = {};
        if (commentNode) {
          commentNode.info.split('\n').forEach(line => {
            const parts = line.split('==');
            if (parts.length === 2) {
              const topic = parts[0].trim();
              const leshanName = parts[1].trim();
              if (topic && leshanName) topicToLeshanName[topic] = leshanName;
            }
          });
        }

        // Group sensors by objectId and assign instanceIds in order within each group
        const objectIdGroups = {};
        mqttNodes.forEach(node => {
          // Try to match objectId/key from SENSOR_TYPES using leshanName or topic
          let leshanName = '';
          const topic = node.topic || '';
          // Use mapping from comment node if available
          leshanName = topicToLeshanName[topic] || '';
          // Fallback: try to find from Leshan object (if mapping not present)
          if (!leshanName && lwm2mClientNode && lwm2mClientNode.objects) {
            const objects = typeof lwm2mClientNode.objects === 'string'
              ? JSON.parse(lwm2mClientNode.objects)
              : lwm2mClientNode.objects;
            Object.keys(objects).forEach(objectType => {
              Object.keys(objects[objectType]).forEach(instanceId => {
                if (objects[objectType][instanceId]['9999'] && node.topic === objects[objectType][instanceId]['9999'].value) {
                  leshanName = objects[objectType][instanceId]['9999'].value;
                }
              });
            });
          }
          // Try to match by leshanName (case-insensitive) or topic
          let sensorType = SENSOR_TYPES.find(t => t.label.toLowerCase() === leshanName.toLowerCase())
            || SENSOR_TYPES.find(t => t.label.toLowerCase() === topic.toLowerCase());
          let objectId = sensorType ? sensorType.objectId : '3303';
          let key = sensorType ? sensorType.key : '5700';
          if (!objectIdGroups[objectId]) objectIdGroups[objectId] = [];
          objectIdGroups[objectId].push({ node, leshanName, key, sensorType });
        });
        const sensorsList = [];
        Object.keys(objectIdGroups).forEach(objectId => {
          objectIdGroups[objectId].forEach((entry, idx) => {
            const { node, leshanName, key, sensorType } = entry;
            let instanceId = String(idx); // assign instanceId in order within group
            let id = node.id || `mqtt_${Date.now()}_${Math.floor(Math.random()*10000)}`;
            const resourceInfo = mqttNodeToLwM2MResourceMap[node.id];
            sensorsList.push({
              id,
              name: node.name || '',
              leshanName,
              topic: node.topic || '',
              hasLwM2MResource: !!resourceInfo,
              resourceInfo: resourceInfo,
              objectId,
              instanceId,
              key,
              type: sensorType ? sensorType.type : '',
              processingFunction: 'none',
            });
          });
        });
        setSensors(sensorsList);
      } catch (error) {
        console.error('Error parsing template:', error);
      }
    }
  }, [templateContent, selectedTemplate]);

  // Handle changes to deployment settings
  const handleDeploymentSettingChange = (e) => {
    const { name, value } = e.target;
    setDeploymentSettings(prev => ({
      ...prev,
      [name]: value
    }));

    // Validate container name
    if (name === 'containerName') {
      if (!value) {
        setErrors(prev => ({ ...prev, containerName: 'Container name is required' }));
      } else if (!/^[a-zA-Z0-9][a-zA-Z0-9_.-]*$/.test(value)) {
        setErrors(prev => ({ ...prev, containerName: 'Invalid container name. Use only letters, numbers, underscores, periods, or hyphens.' }));
      } else {
        setErrors(prev => ({ ...prev, containerName: null }));
      }
    }
  };

  // Handle changes to MQTT broker settings
  const handleMqttBrokerSettingChange = (e) => {
    const { name, value } = e.target;
    setMqttBrokerSettings(prev => ({
      ...prev,
      [name]: name === 'port' ? parseInt(value, 10) || '' : value
    }));

    // Validate MQTT broker settings
    if (name === 'brokerIp' && !value) {
      setErrors(prev => ({ ...prev, brokerIp: 'MQTT broker IP is required' }));
    } else if (name === 'port') {
      if (!value) {
        setErrors(prev => ({ ...prev, port: 'MQTT port is required' }));
      } else if (isNaN(parseInt(value, 10)) || parseInt(value, 10) < 1 || parseInt(value, 10) > 65535) {
        setErrors(prev => ({ ...prev, port: 'Port must be a number between 1 and 65535' }));
      } else {
        setErrors(prev => ({ ...prev, port: null }));
      }
    } else {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  // Add a function to add a new sensor
  const handleAddSensorClick = () => {
    // Default to first non-Custom sensor type
    const defaultType = SENSOR_TYPES.find(t => t.type !== 'Custom');
    setPendingSensor({
      type: defaultType.type,
      objectId: defaultType.objectId,
      key: defaultType.key,
      instanceId: '0',
      name: '',
      leshanName: '',
      topic: '',
      processingFunction: 'none',
      deviceName: ''
    });
    setSensorDialogEdit(false);
    setSensorDialogIndex(null);
    setSensorDialogOpen(true);
  };

  // Edit Sensor
  const handleEditSensor = (index) => {
    setPendingSensor(sensors[index]);
    setSensorDialogEdit(true);
    setSensorDialogIndex(index);
    setSensorDialogOpen(true);
  };

  // Save Sensor (add or edit)
  const handleSaveSensor = (sensor) => {
    let updatedSensors = [...sensors];
    if (sensorDialogEdit && sensorDialogIndex !== null) {
      // Editing: preserve id
      sensor.id = sensors[sensorDialogIndex].id;
      updatedSensors[sensorDialogIndex] = sensor;
    } else {
      // Adding: assign unique id
      sensor.id = sensor.id || `mqtt_${Date.now()}_${Math.floor(Math.random()*10000)}`;
      updatedSensors.push(sensor);
    }
    setSensors(updatedSensors);
    setSensorDialogOpen(false);
    setPendingSensor(null);
    setSensorDialogEdit(false);
    setSensorDialogIndex(null);
  };

  // Remove Sensor
  const handleRemoveSensor = (index) => {
    const updated = [...sensors];
    updated.splice(index, 1);
    setSensors(updated);
  };

  // On deploy, send sensors, leshanObjects, templateName, and all settings
  const handleDeployClick = () => {
    // Check for validation errors
    const hasErrors = Object.values(errors).some(error => error);
    if (hasErrors) {
      return;
    }
    // Always use latest sensors state by defining helpers here
    const buildSensorsForBackend = () => {
      // Return one sensor object per sensor (no grouping)
      return sensors.map(sensor => ({
        id: sensor.id,
        objectId: sensor.objectId,
        instanceId: sensor.instanceId,
        leshanName: sensor.leshanName,
        resources: [{
          key: sensor.key,
          name: sensor.topic,
          instanceId: sensor.instanceId
        }],
        type: sensor.type || '',
        processingFunction: sensor.processingFunction || 'none',
        deviceName: sensor.leshanName || ''
      }));
    };
    const buildLeshanObjects = () => {
      const objects = {};
      sensors.forEach(sensor => {
        const objectId = sensor.objectId || (sensor.resourceInfo && sensor.resourceInfo.objectType) || '';
        const instanceId = sensor.instanceId || (sensor.resourceInfo && sensor.resourceInfo.instanceId) || '0';
        const key = sensor.key || (sensor.resourceInfo && sensor.resourceInfo.key) || '5700';
        if (!objectId) return;
        if (!objects[objectId]) objects[objectId] = {};
        if (!objects[objectId][instanceId]) objects[objectId][instanceId] = { '2': true };
        objects[objectId][instanceId][key] = { type: 'FLOAT', acl: 'RW', value: 0 };
        objects[objectId][instanceId]['9999'] = { type: 'STRING', acl: 'R', value: sensor.leshanName };
      });
      return objects;
    };
    // Build sensors and leshanObjects for backend
    const sensorsForBackend = buildSensorsForBackend();
    const leshanObjects = buildLeshanObjects();
    const payload = {
      sensors: sensorsForBackend,
      leshanObjects,
      templateName: selectedTemplate?.name,
      containerName: deploymentSettings.containerName,
      leshanIpAddress: deploymentSettings.leshanIpAddress,
      mqttBrokerIp: mqttBrokerSettings.brokerIp,
      mqttBrokerPort: mqttBrokerSettings.port,
      mqttUsername: deploymentSettings.mqttUsername,
      mqttPassword: deploymentSettings.mqttPassword
    };
    // Debug log
    console.log('DEPLOY DEBUG:', { sensors, sensorsForBackend, leshanObjects, payload });
    handleDeploy(payload, deploymentSettings);
  };

  // Check if the form is valid
  const isFormValid = () => {
    // Check if there are any validation errors
    if (Object.values(errors).some(error => error)) {
      return false;
    }

    // Check if required fields are filled
    if (!deploymentSettings.containerName || !deploymentSettings.leshanIpAddress) {
      return false;
    }

    if (!mqttBrokerSettings.brokerIp || !mqttBrokerSettings.port) {
      return false;
    }

    // Check if all MQTT topics are filled
    if (sensors.some(node => !node.topic)) {
      return false;
    }

    return true;
  };

  // Sensor Add/Edit Dialog Component
  function SensorDialog({ open, onClose, onSave, sensor, sensorTypes, isEdit }) {
    const [form, setForm] = useState(sensor || {
      type: '',
      objectId: '',
      key: '',
      instanceId: '',
      name: '',
      leshanName: '',
      topic: '',
      processingFunction: 'none',
      deviceName: ''
    });
    const [errors, setErrors] = useState({});

    // eslint-disable-next-line react-hooks/exhaustive-deps
    useEffect(() => {
      // Only reset form when dialog is opening
      if (open) {
        if (sensor) {
          setForm({
            id: sensor?.id || `mqtt_${Date.now()}_${Math.floor(Math.random()*10000)}`,
            type: sensor?.type || '',
            objectId: sensor?.objectId || '3303', // default to temperature object
            key: sensor?.key || '5700', // default key for sensors
            instanceId: sensor?.instanceId || '0',
            name: sensor?.name || '',
            leshanName: sensor?.leshanName || '',
            topic: sensor?.topic || '',
            processingFunction: sensor?.processingFunction || 'none',
            deviceName: sensor?.deviceName || ''
          });
        } else {
          // Default to first non-Custom sensor type
          const defaultType = sensorTypes.find(t => t.type !== 'Custom');
          setForm({
            id: `mqtt_${Date.now()}_${Math.floor(Math.random()*10000)}`,
            type: defaultType.type,
            objectId: defaultType.objectId,
            key: defaultType.key,
            instanceId: '0',
            name: '',
            leshanName: '',
            topic: '',
            processingFunction: 'none',
            deviceName: ''
          });
        }
        setErrors({});
      }
    }, [open, sensor, sensorTypes]);

    // Helper to get type label from objectId/key
    const getTypeLabel = () => {
      if (form.type) {
        const found = sensorTypes.find(t => t.type === form.type && t.objectId === form.objectId && t.key === form.key);
        if (found) return found.label;
      }
      // fallback: try to match by objectId/key
      const found = sensorTypes.find(t => t.objectId === form.objectId && t.key === form.key);
      return found ? found.label : form.type || '';
    };

    const handleChange = (field, value) => {
      setForm(prev => ({ ...prev, [field]: value }));
    };

    const handleTypeChange = (typeLabel) => {
      const typeObj = sensorTypes.find(t => t.label === typeLabel);
      setForm({
        id: `mqtt_${Date.now()}_${Math.floor(Math.random()*10000)}`,
        type: typeObj.type,
        objectId: typeObj.objectId,
        key: typeObj.key,
        name: '',
        leshanName: '',
        topic: '',
        instanceId: '0',
        deviceName: '',
        processingFunction: 'none'
      });
      setErrors({}); // Reset errors on type change
    };

    const validate = () => {
      const errs = {};
      if (!isEdit && !form.type) errs.type = 'Type required';
      if (!form.objectId) errs.objectId = 'Object ID required';
      if (!form.key) errs.key = 'Key required';
      if (!form.leshanName) errs.leshanName = 'Leshan name required';
      if (!form.topic) errs.topic = 'MQTT topic required';
      // Do NOT setErrors here to avoid infinite loop
      return errs;
    };

    // Only enable Save if no errors
    const isSaveEnabled = () => {
      return Object.keys(validate()).length === 0;
    };

    const handleSave = () => {
      const errs = validate();
      setErrors(errs);
      if (Object.keys(errs).length > 0) return;
      onSave(form);
    };

    return (
      <MuiDialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
        <MuiDialogTitle>{isEdit ? 'Edit Sensor' : 'Add Sensor'}</MuiDialogTitle>
        <MuiDialogContent>
          {/* Show validation errors for debugging */}
          {Object.keys(errors).length > 0 && (
            <Box sx={{ mb: 2 }}>
              {Object.entries(errors).map(([key, val]) => (
                <Typography key={key} color="error" variant="body2">{val}</Typography>
              ))}
            </Box>
          )}
          <TextField
            select
            fullWidth
            label="Sensor Type"
            value={getTypeLabel()}
            onChange={e => handleTypeChange(e.target.value)}
            margin="normal"
            disabled={isEdit}
            error={!!errors.type}
            helperText={errors.type}
          >
            {sensorTypes.map((type, idx) => (
              <MenuItem key={type.label + idx} value={type.label}>{type.label}</MenuItem>
            ))}
          </TextField>
          <TextField
            fullWidth
            label="Leshan Name (9999 value)"
            value={form.leshanName}
            onChange={e => handleChange('leshanName', e.target.value)}
            margin="normal"
            error={!!errors.leshanName}
            helperText={errors.leshanName || 'This is the LwM2M/CoAP resource 9999 value (display name)'}
            required
          />
          <TextField
            fullWidth
            label="MQTT Topic"
            value={form.topic}
            onChange={e => handleChange('topic', e.target.value)}
            margin="normal"
            error={!!errors.topic}
            helperText={errors.topic}
          />
          <TextField
            fullWidth
            label="Instance ID"
            value={form.instanceId}
            onChange={e => handleChange('instanceId', e.target.value)}
            margin="normal"
          />
          <TextField
            select
            fullWidth
            label="Processing Function"
            value={form.processingFunction}
            onChange={e => handleChange('processingFunction', e.target.value)}
            margin="normal"
          >
            <MenuItem value="none">None</MenuItem>
            <MenuItem value="average5">Average</MenuItem>
            <MenuItem value="onchange">Send on Change</MenuItem>
          </TextField>
          {form.type === 'Custom' && !isEdit && (
            <>
              <TextField
                fullWidth
                label="Object ID"
                value={form.objectId}
                onChange={e => handleChange('objectId', e.target.value)}
                margin="normal"
                error={!!errors.objectId}
                helperText={errors.objectId}
                required
              />
              <TextField
                fullWidth
                label="Key"
                value={form.key}
                onChange={e => handleChange('key', e.target.value)}
                margin="normal"
                error={!!errors.key}
                helperText={errors.key}
                required
              />
            </>
          )}
        </MuiDialogContent>
        <MuiDialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button variant="contained" onClick={handleSave} disabled={!isSaveEnabled()}>{isEdit ? 'Save' : 'Add'}</Button>
        </MuiDialogActions>
      </MuiDialog>
    );
  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>Edit Template Before Deployment</DialogTitle>
      <DialogContent>
        <DialogContentText component="div" sx={{ mb: 2 }}>
          Configure the template <strong>{selectedTemplate?.name}</strong> before deployment.
        </DialogContentText>

        {/* Container Settings */}
        <Typography variant="subtitle1" gutterBottom sx={{ mt: 2, fontWeight: 'bold' }}>
          Container Settings
        </Typography>
        <Paper elevation={1} sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                margin="normal"
                label="Container Name"
                name="containerName"
                value={deploymentSettings.containerName}
                onChange={handleDeploymentSettingChange}
                helperText={errors.containerName || "This name will be used for both the container and the Leshan endpoint"}
                required
                error={!!errors.containerName}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                margin="normal"
                label="Leshan IP Address"
                name="leshanIpAddress"
                value={deploymentSettings.leshanIpAddress}
                onChange={handleDeploymentSettingChange}
                helperText={errors.leshanIpAddress || "IP address of the Leshan server"}
                required
                error={!!errors.leshanIpAddress}
              />
            </Grid>
          </Grid>
        </Paper>

        {/* MQTT Broker Settings */}
        <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
          MQTT Broker Settings
        </Typography>
        <Paper elevation={1} sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                margin="normal"
                label="MQTT Broker IP"
                name="brokerIp"
                value={mqttBrokerSettings.brokerIp}
                onChange={handleMqttBrokerSettingChange}
                helperText={errors.brokerIp || "IP address of the MQTT broker"}
                required
                error={!!errors.brokerIp}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                margin="normal"
                label="MQTT Port"
                name="port"
                type="number"
                value={mqttBrokerSettings.port}
                onChange={handleMqttBrokerSettingChange}
                helperText={errors.port || "Port of the MQTT broker"}
                required
                error={!!errors.port}
                inputProps={{ min: 1, max: 65535 }}
              />
            </Grid>
          </Grid>
        </Paper>

        {/* Sensors */}
        <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
          Sensors
        </Typography>
        <Stack spacing={2} sx={{ mb: 2 }}>
          {sensors.map((sensor, idx) => (
            <Paper key={idx} sx={{ p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box>
                <Typography variant="subtitle2">{sensor.type} ({sensor.objectId})</Typography>
                <Typography>Leshan Name: {sensor.leshanName || '-'}</Typography>
                <Typography>MQTT Topic: {sensor.topic}</Typography>
                <Typography>Processing: {sensor.processingFunction}</Typography>
              </Box>
              <Box>
                <Button size="small" onClick={() => handleEditSensor(idx)}>Edit</Button>
                <Button size="small" color="error" onClick={() => handleRemoveSensor(idx)}>Remove</Button>
              </Box>
            </Paper>
          ))}
        </Stack>
        <Button variant="outlined" startIcon={<AddIcon />} onClick={handleAddSensorClick} sx={{ mt: 2 }}>
          Add Sensor
        </Button>
        <SensorDialog
          open={sensorDialogOpen}
          onClose={() => setSensorDialogOpen(false)}
          onSave={handleSaveSensor}
          sensor={pendingSensor}
          sensorTypes={SENSOR_TYPES}
          isEdit={sensorDialogEdit}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>Cancel</Button>
        <Button
          variant="contained"
          color="primary"
          onClick={handleDeployClick}
          disabled={loading || !isFormValid()}
          startIcon={loading ? <CircularProgress size={20} /> : <PlayCircleFilled />}
        >
          {loading ? 'Deploying...' : 'Deploy'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TemplateEditor;
