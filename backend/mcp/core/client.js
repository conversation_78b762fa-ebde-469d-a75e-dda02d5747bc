/**
 * Core MCP Client Implementation
 * Handles low-level MCP protocol communication with servers
 * Following the standard implementation from the MCP documentation
 */
const { Client } = require("@modelcontextprotocol/sdk/client/index.js");
const { StdioClientTransport } = require("@modelcontextprotocol/sdk/client/stdio.js");

// Get configuration from environment variables
const INFLUXDB_ORG = process.env.INFLUXDB_ORG || 'ICT';
const INFLUXDB_TOKEN = process.env.INFLUXDB_TOKEN;
const INFLUXDB_URL = process.env.INFLUXDB_URL || 'http://localhost:8086';

// MCP server configuration
const MCP_SERVER_PATH = process.env.MCP_SERVER_PATH;

/**
 * MCP Client class for handling InfluxDB queries using the MCP SDK
 */
class MCPClient {
  constructor() {
    this.client = null;
    this.transport = null;
    this.isConnected = false;
    this.tools = [];
  }

  /**
   * Initialize the MCP client
   * @returns {Promise<boolean>} Whether the initialization was successful
   */
  async initialize() {
    try {
      // Validate required environment variables
      if (!INFLUXDB_TOKEN) {
        throw new Error('INFLUXDB_TOKEN environment variable is not set');
      }

      if (!MCP_SERVER_PATH) {
        throw new Error('MCP_SERVER_PATH environment variable is not set');
      }

      // Check if the MCP server file exists
      const fs = require('fs');
      if (!fs.existsSync(MCP_SERVER_PATH)) {
        throw new Error(`MCP server file not found at ${MCP_SERVER_PATH}`);
      }

      // Create a new MCP client with client info
      this.client = new Client({
        name: "InfluxDB MCP Client",
        version: "1.0.0"
      });

      // Create a transport for the MCP server
      this.transport = new StdioClientTransport({
        command: 'node',
        args: [MCP_SERVER_PATH],
        env: {
          ...process.env,
          INFLUXDB_TOKEN,
          INFLUXDB_ORG,
          INFLUXDB_URL
        }
      });

      // Set up error handler
      this.client.onerror = (error) => {
        console.error('MCP client error:', error);
      };

      // Connect to the transport
      await this.client.connect(this.transport);

      // Get the list of available tools
      const toolsResponse = await this.client.listTools();
      this.tools = toolsResponse.tools;

      // Set connected flag
      this.isConnected = true;
      return true;
    } catch (error) {
      console.error('Failed to initialize MCP client:', error);
      this.isConnected = false;
      return false;
    }
  }

  /**
   * Get the list of available tools
   * @returns {Array} List of tools
   */
  getTools() {
    return this.tools;
  }

  /**
   * Call a tool
   * @param {string} toolName - Name of the tool to call
   * @param {Object} params - Parameters for the tool
   * @returns {Promise<Object>} Result of the tool call
   */
  async callTool(toolName, params) {
    try {
      if (!this.isConnected) {
        throw new Error('MCP client is not connected');
      }

      // Find the tool
      const tool = this.tools.find(t => t.name === toolName);
      if (!tool) {
        throw new Error(`Unknown tool: ${toolName}`);
      }

      // Call the tool using the MCP client
      const response = await this.client.callTool({
        name: toolName,
        arguments: params
      });

      return response;
    } catch (error) {
      console.error(`Error calling tool ${toolName}:`, error);
      throw error;
    }
  }

  /**
   * Extract content from a tool result
   * @param {Object} result - Tool result
   * @returns {string} Extracted content
   */
  extractContent(result) {
    try {
      if (!result || !result.content) {
        return '';
      }

      // If content is an array, join it
      if (Array.isArray(result.content)) {
        return result.content.map(item => {
          if (typeof item === 'string') {
            return item;
          } else if (item.text) {
            return item.text;
          } else if (item.type === 'text' && item.text) {
            return item.text;
          }
          return JSON.stringify(item);
        }).join('\n');
      }

      // If content is a string, return it
      if (typeof result.content === 'string') {
        return result.content;
      }

      // If content is an object with text property, return it
      if (result.content.text) {
        return result.content.text;
      }

      // Otherwise, stringify the content
      return JSON.stringify(result.content);
    } catch (error) {
      console.error('Error extracting content:', error);
      return '';
    }
  }

  /**
   * Close the MCP client
   */
  async close() {
    if (this.client && this.transport) {
      try {
        await this.client.disconnect();
        this.isConnected = false;
      } catch (error) {
        console.error('Error closing MCP client:', error);
      }
    }
  }
}

// Create and export a singleton instance
const mcpClient = new MCPClient();
module.exports = mcpClient;
