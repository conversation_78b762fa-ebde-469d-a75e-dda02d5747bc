import React from 'react';
import { Routes, Route } from 'react-router-dom';
import MainLayout from './layouts/MainLayout';
import Home from './pages/Home';
import CreateFromScratch from './pages/CreateFromScratch';
import Login from './pages/Login';
import ContainerManagement from './pages/ContainerManagement';
import TemplateManagement from './pages/TemplateManagement';
import ModernAGUIPage from './mcp-client/pages/ModernAGUIPage';

import './App.css';


function App() {
    return (
        <Routes>
            {/* Routes with Layout */}
            <Route path="/" element={<MainLayout />}>
                <Route index element={<Home />} />
                <Route path="/create-from-scratch" element={<CreateFromScratch />} />
                <Route path="/management" element={<ContainerManagement />} />
                <Route path="/templates" element={<TemplateManagement />} />
            </Route>

            {/* Standalone Routes */}
            <Route path="/login" element={<Login />} />
            <Route path="/chats" element={<ModernAGUIPage />} />
        </Routes>
    );
}

export default App;
