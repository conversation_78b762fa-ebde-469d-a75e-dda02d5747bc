const VirtuMeshLogo = ({ height = 40, darkMode = false }) => {
    const textColor = darkMode ? "#ffffff" : "#263238";
    const subTextColor = darkMode ? "#cfd8dc" : "#607D8B";
  
    return (
      <svg
        viewBox="0 0 420 160"
        preserveAspectRatio="xMidYMid meet"
        xmlns="http://www.w3.org/2000/svg"
        style={{ height: `${height}px`, width: 'auto' }}
      >
        {/* Mesh/Network Motif */}
        <g>
          <circle cx="60" cy="80" r="8" fill="#2196F3" />
          <circle cx="100" cy="40" r="8" fill="#4CAF50" />
          <circle cx="140" cy="80" r="8" fill="#FFC107" />
          <circle cx="100" cy="120" r="8" fill="#00BCD4" />
          <line x1="60" y1="80" x2="100" y2="40" stroke="#90CAF9" strokeWidth="4" />
          <line x1="100" y1="40" x2="140" y2="80" stroke="#A5D6A7" strokeWidth="4" />
          <line x1="140" y1="80" x2="100" y2="120" stroke="#FFF59D" strokeWidth="4" />
          <line x1="100" y1="120" x2="60" y2="80" stroke="#80DEEA" strokeWidth="4" />
          <line x1="60" y1="80" x2="140" y2="80" stroke="#B0BEC5" strokeWidth="2" strokeDasharray="6,6" />
          <line x1="100" y1="40" x2="100" y2="120" stroke="#B0BEC5" strokeWidth="2" strokeDasharray="6,6" />
        </g>
  
        {/* Abstract V Shape */}
        <polyline
          points="60,80 100,120 140,80"
          fill="none"
          stroke={textColor}
          strokeWidth="6"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
  
        {/* Brand Name */}
        <text
          x="170"
          y="90"
          fontFamily="Segoe UI, Arial, sans-serif"
          fontSize="48"
          fill={textColor}
          fontWeight="bold"
        >
          VirtuMesh
        </text>
  
        {/* Tagline */}
        <text
          x="170"
          y="120"
          fontFamily="Segoe UI, Arial, sans-serif"
          fontSize="20"
          fill={subTextColor}
        >
          Digital Integration Platform
        </text>
      </svg>
    );
  };
  
  export default VirtuMeshLogo;
  