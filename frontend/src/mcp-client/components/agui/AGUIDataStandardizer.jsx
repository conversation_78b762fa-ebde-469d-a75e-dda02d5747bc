/**
 * AG-UI Data Standardizer Component
 * Automatically detects and standardizes data visualization based on AG-UI protocol
 * Handles diverse data types dynamically without hardcoded responses
 */

import React, { useMemo } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  Alert,
  Chip
} from '@mui/material';
import {
  Table<PERSON>hart as TableIcon,
  BarChart as ChartIcon,
  Code as JsonIcon,
  TextFields as TextIcon,
  DataObject as DataIcon
} from '@mui/icons-material';

// Simple table and chart components for AG-UI data visualization
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination
} from '@mui/material';

// Import Recharts for data visualization
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

// Helper function to extract raw data from agent state
const extractRawData = (agentState) => {
  const toolResults = agentState.toolResults || [];
  
  // Extract from tool results
  for (const tool of toolResults) {
    const content = tool.result?.content;
    
    if (Array.isArray(content)) {
      const textContent = content.find(item => 
        item.text?.trim() && item.text !== '\r\n'
      );
      if (textContent) {
        return textContent.text;
      }
    } else if (typeof content === 'string' && content.trim()) {
      return content;
    }
  }
  
  // Fallback to rawData
  return agentState.rawData || null;
};

// Helper function to analyze data type and structure
const analyzeDataStructure = (rawData) => {
  let dataType = 'unknown';
  let availableViews = ['text'];
  let processedData = rawData;

  if (typeof rawData === 'string') {
    // Check for CSV format
    if (rawData.includes(',') && rawData.includes('\n')) {
      const csvData = parseCSVData(rawData);
      if (csvData) {
        dataType = 'tabular';
        processedData = csvData;
        availableViews = ['table', 'chart', 'json', 'text'];
        return { dataType, processedData, availableViews };
      }
    }

    // Check for JSON format
    try {
      const jsonData = JSON.parse(rawData);
      if (Array.isArray(jsonData) && jsonData.length > 0) {
        dataType = 'array';
        processedData = jsonData;
        availableViews = ['table', 'json', 'text'];
        if (typeof jsonData[0] === 'object') {
          availableViews.push('chart');
        }
      } else if (typeof jsonData === 'object' && jsonData !== null) {
        dataType = 'object';
        processedData = jsonData;
        availableViews = ['json', 'text'];
      }
    } catch (e) {
      // Not JSON, keep as text
      dataType = 'text';
      availableViews = ['text'];
    }
  } else if (Array.isArray(rawData)) {
    dataType = 'array';
    availableViews = ['table', 'json', 'text'];
    if (rawData.length > 0 && typeof rawData[0] === 'object') {
      availableViews.push('chart');
    }
  } else if (typeof rawData === 'object' && rawData !== null) {
    dataType = 'object';
    availableViews = ['json', 'text'];
  }

  return { dataType, processedData, availableViews };
};

// Helper function to parse CSV data
const parseCSVData = (rawData) => {
  try {
    const lines = rawData.split('\n').filter(line => line.trim());
    
    if (lines.length <= 1) {
      return null;
    }
    
    const headers = lines[0].split(',').map(h => h.trim());
    
    const rows = lines.slice(1).map(line => {
      const values = line.split(',');
      const obj = {};
      headers.forEach((header, index) => {
        obj[header] = values[index]?.trim() || '';
      });
      return obj;
    });

    return rows.length > 0 ? rows : null;
  } catch (e) {
    console.warn('Failed to parse CSV data:', e);
    return null;
  }
};

// Simple Table Component
const SimpleTable = ({ data, title }) => {
  const [page, setPage] = React.useState(0);
  const [rowsPerPage, setRowsPerPage] = React.useState(10);

  const columns = Object.keys(data[0] || {});
  const paginatedData = data.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  return (
    <Paper variant="outlined" sx={{ width: '100%' }}>
      <Typography variant="h6" sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        {title}
      </Typography>
      <TableContainer sx={{ maxHeight: 400 }}>
        <Table stickyHeader size="small">
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <TableCell key={column} sx={{ fontWeight: 'bold' }}>
                  {column}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedData.map((row, index) => (
              <TableRow key={index} hover>
                {columns.map((column) => (
                  <TableCell key={column}>
                    {String(row[column] || '')}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        component="div"
        count={data.length}
        page={page}
        onPageChange={(e, newPage) => setPage(newPage)}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={(e) => {
          setRowsPerPage(parseInt(e.target.value, 10));
          setPage(0);
        }}
        rowsPerPageOptions={[5, 10, 25, 50]}
      />
    </Paper>
  );
};

// Smart Chart Component using Recharts
const SimpleChart = ({ data, title }) => {
  // Analyze data structure to determine best chart type and fields
  const sampleRow = data[0] || {};
  const fields = Object.keys(sampleRow);

  // Find potential X-axis field (time, date, or first field)
  let xField = fields.find(field =>
    field.toLowerCase().includes('time') ||
    field.toLowerCase().includes('date') ||
    field.toLowerCase().includes('timestamp')
  ) || fields[0];

  // Find numeric fields for Y-axis
  const numericFields = fields.filter(field => {
    if (field === xField) return false;
    // Check if most values in this field are numeric
    const numericCount = data.slice(0, 10).reduce((count, row) => {
      const value = row[field];
      return count + (value !== null && value !== undefined && !isNaN(Number(value)) ? 1 : 0);
    }, 0);
    return numericCount >= Math.min(1, data.length * 0.5); // At least half should be numeric
  });

  // Use available fields for charting, even if not all are numeric
  const fieldsToChart = numericFields.length > 0 ? numericFields : fields.slice(1, 3);

  // Prepare data for charting
  const chartData = data.map((row, index) => {
    const chartRow = { index };

    // Add X-axis value
    let xValue = row[xField];
    if (xValue && typeof xValue === 'string') {
      // Try to parse as date/time
      const dateValue = new Date(xValue);
      if (!isNaN(dateValue.getTime())) {
        xValue = dateValue.toLocaleTimeString();
      }
    }
    chartRow[xField] = xValue || index;

    // Add chart fields
    fieldsToChart.forEach(field => {
      const value = row[field];
      chartRow[field] = value !== null && value !== undefined && !isNaN(Number(value))
        ? Number(value)
        : null;
    });

    return chartRow;
  });

  // Determine chart type based on data characteristics
  const isTimeSeries = xField.toLowerCase().includes('time') ||
                      xField.toLowerCase().includes('date');

  // Color palette matching your app's design system
  const colors = ['#6B46C1', '#319795', '#9F7AEA', '#4FD1C5', '#553C9A', '#2C7A7B'];

  return (
    <Paper variant="outlined" sx={{ p: 2 }}>
      <Typography variant="h6" gutterBottom>{title}</Typography>
      <Box sx={{ width: '100%', height: 400 }}>
        <ResponsiveContainer>
          {isTimeSeries ? (
            <AreaChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey={xField}
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={60}
              />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip
                labelStyle={{ color: '#333' }}
                contentStyle={{ backgroundColor: '#fff', border: '1px solid #ccc' }}
              />
              <Legend />
              {numericFields.slice(0, 3).map((field, index) => (
                <Area
                  key={field}
                  type="monotone"
                  dataKey={field}
                  stroke={colors[index]}
                  fill={colors[index]}
                  fillOpacity={0.3}
                  strokeWidth={2}
                />
              ))}
            </AreaChart>
          ) : (
            <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey={xField}
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={60}
              />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip
                labelStyle={{ color: '#333' }}
                contentStyle={{ backgroundColor: '#fff', border: '1px solid #ccc' }}
              />
              <Legend />
              {fieldsToChart.slice(0, 3).map((field, index) => (
                <Line
                  key={field}
                  type="monotone"
                  dataKey={field}
                  stroke={colors[index]}
                  strokeWidth={2}
                  dot={{ r: 3 }}
                />
              ))}
            </LineChart>
          )}
        </ResponsiveContainer>
      </Box>

      {/* Chart Info */}
      <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
        <Chip
          label={`X-axis: ${xField}`}
          size="small"
          variant="outlined"
        />
        {fieldsToChart.slice(0, 3).map((field, index) => (
          <Chip
            key={field}
            label={field}
            size="small"
            sx={{
              bgcolor: colors[index],
              color: 'white',
              '& .MuiChip-label': { color: 'white' }
            }}
          />
        ))}
        {fieldsToChart.length > 3 && (
          <Chip
            label={`+${fieldsToChart.length - 3} more`}
            size="small"
            variant="outlined"
          />
        )}
      </Box>
    </Paper>
  );
};

/**
 * AG-UI Data Standardizer - Automatically handles any data type
 */
const AGUIDataStandardizer = ({ agentState, onFeedback }) => {
  const dataAnalysis = useMemo(() => {
    const hasData = agentState?.dataAvailable;
    
    if (!hasData) {
      return { type: 'none', data: null, views: [] };
    }

    // Extract data from toolResults or rawData
    const rawData = extractRawData(agentState);
    
    if (!rawData) {
      return { type: 'none', data: null, views: [] };
    }

    // Analyze data structure
    const { dataType, processedData, availableViews } = analyzeDataStructure(rawData);

    const result = {
      type: dataType,
      data: processedData,
      views: availableViews,
      recordCount: Array.isArray(processedData) ? processedData.length : null
    };
    
    return result;
  }, [agentState]);

  const [activeView, setActiveView] = React.useState(0);

  // Auto-select best view based on available views
  React.useEffect(() => {
    const viewPriority = ['chart', 'table', 'json', 'text'];
    const preferredView = viewPriority.find(view => dataAnalysis.views.includes(view));
    const viewIndex = preferredView ? dataAnalysis.views.indexOf(preferredView) : 0;
    setActiveView(viewIndex);
  }, [dataAnalysis]);

  if (dataAnalysis.type === 'none') {
    return (
      <Alert severity="info" sx={{ mt: 2 }}>
        <Typography variant="body2">
          No structured data available from this query.
        </Typography>
      </Alert>
    );
  }

  const renderView = (viewType) => {
    const { data } = dataAnalysis;

    switch (viewType) {
      case 'table':
        return (
          <SimpleTable
            data={data}
            title={`Data Table (${data.length} records)`}
          />
        );

      case 'chart':
        return (
          <SimpleChart
            data={data}
            title={`Data Visualization (${data.length} points)`}
          />
        );

      case 'json':
        return (
          <Paper variant="outlined" sx={{ p: 2, maxHeight: 400, overflow: 'auto' }}>
            <Typography
              component="pre"
              variant="body2"
              sx={{
                fontFamily: 'monospace',
                fontSize: '0.8rem',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word'
              }}
            >
              {JSON.stringify(data, null, 2)}
            </Typography>
          </Paper>
        );

      case 'text':
        const textData = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
        return (
          <Paper variant="outlined" sx={{ p: 2, maxHeight: 400, overflow: 'auto' }}>
            <Typography
              variant="body2"
              sx={{
                fontFamily: 'monospace',
                fontSize: '0.8rem',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word'
              }}
            >
              {textData}
            </Typography>
          </Paper>
        );

      default:
        return <Typography>Unknown view type: {viewType}</Typography>;
    }
  };

  const getViewIcon = (viewType) => {
    switch (viewType) {
      case 'table': return <TableIcon />;
      case 'chart': return <ChartIcon />;
      case 'json': return <JsonIcon />;
      case 'text': return <TextIcon />;
      default: return <DataIcon />;
    }
  };

  const getViewLabel = (viewType) => {
    switch (viewType) {
      case 'table': return 'Table';
      case 'chart': return 'Chart';
      case 'json': return 'JSON';
      case 'text': return 'Raw';
      default: return viewType;
    }
  };

  return (
    <Box sx={{ mt: 2 }}>
      {/* Data Info */}
      <Box sx={{ mb: 2, display: 'flex', gap: 1, flexWrap: 'wrap', alignItems: 'center' }}>
        <Chip
          icon={<DataIcon />}
          label={`Type: ${dataAnalysis.type}`}
          size="small"
          color="primary"
          variant="outlined"
        />
        {dataAnalysis.recordCount && (
          <Chip
            label={`${dataAnalysis.recordCount} records`}
            size="small"
            color="secondary"
            variant="outlined"
          />
        )}
        <Chip
          label={`${dataAnalysis.views.length} views`}
          size="small"
          color="info"
          variant="outlined"
        />
      </Box>

      {/* View Tabs */}
      {dataAnalysis.views.length > 1 && (
        <Tabs
          value={activeView}
          onChange={(e, newValue) => setActiveView(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
        >
          {dataAnalysis.views.map((viewType, index) => (
            <Tab
              key={viewType}
              icon={getViewIcon(viewType)}
              label={getViewLabel(viewType)}
              iconPosition="start"
            />
          ))}
        </Tabs>
      )}

      {/* View Content */}
      <Box>
        {renderView(dataAnalysis.views[activeView])}
      </Box>
    </Box>
  );
};

export default AGUIDataStandardizer;
