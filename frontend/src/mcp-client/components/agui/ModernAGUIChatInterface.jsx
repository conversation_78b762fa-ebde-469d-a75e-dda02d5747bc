/**
 * Modern AG-UI Chat Interface - Claude.ai/ChatGPT Inspired
 * Minimalist, centered design with hidden sidebar and clean mode switching
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Avatar,
  CircularProgress,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  Drawer,
  AppBar,
  Toolbar,
  Paper,
  Fade,
  Collapse,
  useTheme,
  alpha,
  Container
} from '@mui/material';
import {
  Send as SendIcon,
  Add as AddIcon,
  Menu as MenuIcon,
  Chat as ChatIcon,
  Psychology as PsychologyIcon,
  Storage as StorageIcon,
  SmartToy as BotIcon,
  Clear as ClearIcon,
  AutoAwesome as AIIcon,
  DataObject as DataIcon
} from '@mui/icons-material';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import { motion, AnimatePresence } from 'framer-motion';
import { v4 as uuidv4 } from 'uuid';

import AGUIAgent, { EventType } from '../../services/agui/AGUIAgent';
import AGUIMessage from './AGUIMessage';
import AGUIErrorBoundary from './AGUIErrorBoundary';

const API_HOST = process.env.REACT_APP_API_HOST || 'localhost';
const API_BASE_URL = `http://${API_HOST}:5000`;

// Modern theme inspired by Claude.ai and ChatGPT
const modernTheme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#2563eb',
      light: '#3b82f6',
      dark: '#1d4ed8',
    },
    secondary: {
      main: '#7c3aed',
      light: '#8b5cf6',
      dark: '#6d28d9',
    },
    background: {
      default: '#ffffff',
      paper: '#ffffff',
    },
    text: {
      primary: '#1f2937',
      secondary: '#6b7280',
    },
    grey: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
    }
  },
  typography: {
    fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    h4: {
      fontWeight: 700,
      letterSpacing: '-0.025em',
    },
    h6: {
      fontWeight: 600,
      letterSpacing: '-0.015em',
    },
    body1: {
      lineHeight: 1.6,
    }
  },
  shape: {
    borderRadius: 12,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 500,
          borderRadius: '8px',
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: '12px',
            backgroundColor: '#f9fafb',
            border: 'none',
            '& fieldset': {
              border: 'none',
            },
            '&:hover': {
              backgroundColor: '#f3f4f6',
            },
            '&.Mui-focused': {
              backgroundColor: '#ffffff',
              boxShadow: '0 0 0 2px rgba(37, 99, 235, 0.2)',
            },
          },
        },
      },
    },
  },
});

// Query modes
const QUERY_MODES = {
  MCP: 'mcp',
  LLM: 'llm'
};

// Mode configurations
const MODE_CONFIG = {
  [QUERY_MODES.MCP]: {
    label: 'MCP Mode',
    description: 'Query and analyze IoT sensor data',
    icon: <DataIcon />,
    color: '#2563eb',
    gradient: 'linear-gradient(135deg, #2563eb, #1d4ed8)'
  },
  [QUERY_MODES.LLM]: {
    label: 'LLM Mode',
    description: 'General AI conversation',
    icon: <AIIcon />,
    color: '#7c3aed',
    gradient: 'linear-gradient(135deg, #7c3aed, #6d28d9)'
  }
};

const SIDEBAR_WIDTH = 280;

const ModernAGUIChatInterface = ({
  agentConfig = {},
  onStateChange,
  onMessageAdd,
  tools = [],
  context = []
}) => {
  // Core state
  const [agent, setAgent] = useState(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [isRunning, setIsRunning] = useState(false);
  const [agentState, setAgentState] = useState({});
  const [error, setError] = useState(null);

  // UI state
  const [queryMode, setQueryMode] = useState(QUERY_MODES.MCP);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sessions, setSessions] = useState([
    { id: uuidv4(), title: 'New conversation', timestamp: Date.now() }
  ]);
  const [currentSession, setCurrentSession] = useState(sessions[0].id);
  const [streamingMessage, setStreamingMessage] = useState(null);
  const [showWelcome, setShowWelcome] = useState(true);

  // Refs
  const messagesEndRef = useRef(null);
  const runSubscriptionRef = useRef(null);
  const inputRef = useRef(null);
  
  const theme = useTheme();

  // Ref to track current subscription 
  const currentSubscriptionRef = useRef(null);

  // Check if conversation has started
  const hasMessages = messages.length > 0;

  // Scroll to bottom when new messages arrive
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // Initialize agent
  useEffect(() => {
    const initializeAgent = async () => {
      try {
        const config = {
          agentId: 'modern-digital-twin-assistant',
          baseUrl: agentConfig.baseUrl || API_BASE_URL,
          ...agentConfig
        };

        const newAgent = new AGUIAgent(config);
        
        setAgent(newAgent);
        setIsInitialized(true);
        setError(null);
      } catch (error) {
        console.error('Failed to initialize agent:', error);
        setError('Failed to connect to the AI agent. Please check your connection.');
      }
    };

    initializeAgent();
  }, [agentConfig]);

  // Cleanup subscriptions on unmount
  useEffect(() => {
    return () => {
      currentSubscriptionRef.current?.unsubscribe();
      currentSubscriptionRef.current = null;
    };
  }, []);

  // Handle sending messages
  const handleSend = useCallback(async () => {
    if (isRunning || !input.trim()) return;

    // Clean up any existing subscription before starting new one
    currentSubscriptionRef.current?.unsubscribe();
    currentSubscriptionRef.current = null;

    const userMessage = {
      id: uuidv4(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsRunning(true);
    setError(null);
    setShowWelcome(false);

    try {
      // Create run input
      const runInput = {
        messages: [...messages, userMessage],
        tools: queryMode === QUERY_MODES.MCP ? tools : [],
        context: {
          ...context,
          queryMode
        }
      };

      // Run the agent and collect events
      const events$ = agent.runAgent(runInput);
      
      // Subscribe to agent run events for real-time streaming and status updates
      const subscription = events$.subscribe({
        next: (event) => {
          switch (event.type) {
            case 'TEXT_MESSAGE_START':
              // Add streaming message for real-time updates
              const streamingMessage = {
                id: event.messageId,
                role: 'assistant',
                content: '',
                timestamp: new Date().toISOString(),
                isStreaming: true
              };
              
              setMessages(prev => [...prev, streamingMessage]);
              break;
              
            case 'TEXT_MESSAGE_CONTENT':
              setMessages(prev => 
                prev.map(msg => 
                  msg.id === event.messageId 
                    ? { ...msg, content: msg.content + event.delta }
                    : msg
                )
              );
              break;
              
            case 'STATE_SNAPSHOT':
              // Update the current streaming message with toolResults and data
              setMessages(prev => 
                prev.map(msg => {
                  // Find the current assistant message (should be the last one)
                  if (msg.role === 'assistant' && prev.indexOf(msg) === prev.length - 1) {
                    return {
                      ...msg,
                      toolResults: event.snapshot.toolResults || [],
                      data: event.snapshot.rawData,
                      metadata: event.snapshot.metadata
                    };
                  }
                  return msg;
                })
              );
              
              // Also update agentState for the AGUIDataStandardizer
              setAgentState(prev => ({
                ...prev,
                ...event.snapshot,
                dataAvailable: event.snapshot.dataAvailable || !!(event.snapshot.toolResults?.length),
                toolResults: event.snapshot.toolResults || [],
                rawData: event.snapshot.rawData,
                metadata: event.snapshot.metadata
              }));
              break;
              
            case 'TEXT_MESSAGE_END':
              setMessages(prev => 
                prev.map(msg => 
                  msg.id === event.messageId 
                    ? { ...msg, isStreaming: false }
                    : msg
                )
              );
              break;
              
            case 'RUN_STARTED':
              console.log('[Chat] Agent run started');
              break;
            case 'RUN_FINISHED':
              console.log('[Chat] Agent run finished');
              break;
            case 'RUN_ERROR':
              console.error('[Chat] Agent run error:', event.message);
              setError(event.message || 'Agent run failed');
              break;
          }
        },
        error: (error) => {
          console.error('Agent run error:', error);
          throw error;
        },
        complete: () => {
          console.log('[Chat] Agent run completed');
          currentSubscriptionRef.current = null;
        }
      });

      // Store the subscription in the ref
      currentSubscriptionRef.current = subscription;
    } catch (error) {
      console.error('Query failed:', error);
      setError(error.message || 'Failed to get response from the AI agent');
      
      const errorMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: '❌ Sorry, I encountered an error processing your request. Please try again.',
        timestamp: new Date().toISOString(),
        isError: true
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsRunning(false);
    }
  }, [input, agent, isRunning, queryMode, currentSession, context, tools, onMessageAdd]);

  // Handle key press
  const handleKeyPress = useCallback((event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSend();
    }
  }, [handleSend]);

  // Handle new conversation
  const handleNewConversation = useCallback(() => {
    const newSession = {
      id: uuidv4(),
      title: 'New conversation',
      timestamp: Date.now()
    };
    
    setSessions(prev => [newSession, ...prev]);
    setCurrentSession(newSession.id);
    setMessages([]);
    setError(null);
    setShowWelcome(true);
    setSidebarOpen(false);
  }, []);

  // Handle mode switch
  const handleModeSwitch = useCallback((newMode) => {
    setQueryMode(newMode);
    setError(null);
  }, []);

  // Welcome screen component
  const WelcomeScreen = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.5 }}
    >
        <Box sx={{
          display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
          justifyContent: 'center',
        minHeight: { xs: '50vh', sm: '60vh' },
        textAlign: 'center',
        px: { xs: 2, sm: 3 },
        pt: { xs: 10, sm: 8, md: 6 } // Add top padding to account for floating controls
      }}>
        <motion.div
          animate={{ 
            scale: [1, 1.05, 1],
            rotate: [0, 5, -5, 0]
          }}
          transition={{ duration: 4, repeat: Infinity }}
        >
          <Avatar sx={{
            width: { xs: 60, sm: 80 },
            height: { xs: 60, sm: 80 },
            mb: { xs: 2, sm: 3 },
            background: MODE_CONFIG[queryMode].gradient,
            fontSize: { xs: '1.5rem', sm: '2rem' }
          }}>
            {MODE_CONFIG[queryMode].icon}
              </Avatar>
        </motion.div>
        
        <Typography variant="h4" sx={{ 
          mb: 2, 
          fontWeight: 700,
          fontSize: { xs: '1.75rem', sm: '2.125rem' },
          background: MODE_CONFIG[queryMode].gradient,
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}>
          Digital Twin Assistant
        </Typography>
        
        <Typography variant="body1" color="text.secondary" sx={{ 
          mb: { xs: 3, sm: 4 }, 
          maxWidth: 500,
          fontSize: { xs: '0.9rem', sm: '1rem' }
        }}>
          {MODE_CONFIG[queryMode].description}. Switch modes using the toggle or start a conversation below.
              </Typography>

        <Box sx={{ 
          display: 'flex', 
          gap: { xs: 1, sm: 2 }, 
          flexWrap: 'wrap', 
          justifyContent: 'center' 
        }}>
          {queryMode === QUERY_MODES.MCP ? (
            <>
              <Chip label="📊 Query sensor data" variant="outlined" />
              <Chip label="📈 Analyze trends" variant="outlined" />
              <Chip label="🔍 Monitor systems" variant="outlined" />
            </>
          ) : (
            <>
              <Chip label="💬 General conversation" variant="outlined" />
              <Chip label="📝 Code assistance" variant="outlined" />
              <Chip label="🧠 Problem solving" variant="outlined" />
            </>
          )}
        </Box>
            </Box>
    </motion.div>
  );

  // Modern mode switcher
  const ModeSwitcher = () => (
    <Box sx={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: 1,
      order: { xs: 3, md: 2 },
      width: { xs: '100%', md: 'auto' },
      justifyContent: { xs: 'center', md: 'flex-start' }
    }}>
      <Paper sx={{
        display: 'flex',
        borderRadius: { xs: '8px', sm: '12px' },
        p: { xs: 0.3, sm: 0.5 },
        backgroundColor: 'grey.100',
        border: '1px solid',
        borderColor: 'grey.200'
      }}>
        {Object.entries(MODE_CONFIG).map(([mode, config]) => (
          <motion.div
            key={mode}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Button
              onClick={() => handleModeSwitch(mode)}
              sx={{
                minWidth: { xs: 90, sm: 120 },
                py: { xs: 0.5, sm: 1 },
                px: { xs: 1.5, sm: 2 },
                borderRadius: { xs: '6px', sm: '8px' },
                textTransform: 'none',
                fontWeight: 600,
                fontSize: { xs: '0.75rem', sm: '0.875rem' },
                transition: 'all 0.2s ease',
                ...(queryMode === mode ? {
                  background: config.gradient,
                  color: 'white',
                  boxShadow: `0 2px 8px ${alpha(config.color, 0.3)}`
                } : {
                  color: 'text.secondary',
                  '&:hover': {
                    backgroundColor: alpha(config.color, 0.05)
                  }
                })
              }}
            >
              <Box sx={{ 
                mr: { xs: 0.5, sm: 1 }, 
                display: 'flex', 
                alignItems: 'center',
                '& svg': { fontSize: { xs: '1rem', sm: '1.25rem' } }
              }}>
                {config.icon}
              </Box>
              <Box sx={{ display: { xs: 'none', sm: 'block' } }}>
                {config.label}
              </Box>
              <Box sx={{ display: { xs: 'block', sm: 'none' } }}>
                {mode.toUpperCase()}
              </Box>
            </Button>
          </motion.div>
        ))}
      </Paper>
          </Box>
  );

  // Sidebar component
  const Sidebar = () => (
    <Drawer
      anchor="left"
      open={sidebarOpen}
      onClose={() => setSidebarOpen(false)}
      sx={{
        '& .MuiDrawer-paper': {
          width: SIDEBAR_WIDTH,
          backgroundColor: 'grey.50',
          borderRight: '1px solid',
          borderColor: 'grey.200'
        }
      }}
    >
      <Box sx={{ p: 2 }}>
        <Button
          fullWidth
          startIcon={<AddIcon />}
          onClick={handleNewConversation}
          sx={{
            mb: 2,
            justifyContent: 'flex-start',
            backgroundColor: 'primary.main',
            color: 'white',
            '&:hover': {
              backgroundColor: 'primary.dark'
            }
          }}
        >
          New Conversation
        </Button>
        
        <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
          Recent Conversations
                        </Typography>
        
        {sessions.map((session) => (
          <Button
            key={session.id}
              fullWidth
            onClick={() => {
              setCurrentSession(session.id);
              setSidebarOpen(false);
            }}
            sx={{
              justifyContent: 'flex-start',
              mb: 0.5,
              color: currentSession === session.id ? 'primary.main' : 'text.secondary',
              backgroundColor: currentSession === session.id ? alpha(theme.palette.primary.main, 0.1) : 'transparent'
            }}
          >
            <ChatIcon sx={{ mr: 1, fontSize: '1rem' }} />
            <Typography variant="body2" sx={{ textAlign: 'left', overflow: 'hidden', textOverflow: 'ellipsis' }}>
              {session.title}
            </Typography>
          </Button>
        ))}
          </Box>
    </Drawer>
  );

  return (
    <ThemeProvider theme={modernTheme}>
      <Box sx={{
        height: '100vh',
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: 'background.default'
      }}>
        <Sidebar />

        {/* Main Chat Area */}
        <Box sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          position: 'relative'
        }}>
          {/* Floating Controls */}
          <Box sx={{
            position: 'absolute',
            top: { xs: 8, sm: 16 },
            left: { xs: 8, sm: 16 },
            right: { xs: 8, sm: 16 },
            zIndex: 10,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            flexWrap: { xs: 'wrap', md: 'nowrap' },
            gap: { xs: 1, sm: 2 }
          }}>
            <IconButton
              onClick={() => setSidebarOpen(true)}
                    sx={{
                color: 'text.secondary',
                backgroundColor: 'background.paper',
                boxShadow: 1,
                width: { xs: 36, sm: 40 },
                height: { xs: 36, sm: 40 },
                order: { xs: 1, md: 1 },
                '&:hover': {
                  backgroundColor: 'grey.100'
                },
                '& svg': { fontSize: { xs: '1.2rem', sm: '1.5rem' } }
              }}
            >
              <MenuIcon />
            </IconButton>

            <ModeSwitcher />

            <IconButton
              onClick={handleNewConversation}
              sx={{ 
                color: 'text.secondary',
                backgroundColor: 'background.paper',
                boxShadow: 1,
                width: { xs: 36, sm: 40 },
                height: { xs: 36, sm: 40 },
                order: { xs: 2, md: 3 },
                '&:hover': {
                  backgroundColor: 'grey.100'
                },
                '& svg': { fontSize: { xs: '1.2rem', sm: '1.5rem' } }
              }}
            >
              <AddIcon />
            </IconButton>
          </Box>
          {/* Messages Container */}
          <Box sx={{
            flex: 1,
            overflow: 'auto',
            py: hasMessages ? 2 : 0,
            pt: hasMessages ? { xs: 12, sm: 10, md: 8 } : 0 // Add responsive top padding when there are messages to account for floating controls
          }}>
            <Container maxWidth="md" sx={{ height: '100%' }}>
              <AnimatePresence mode="wait">
                {!hasMessages && showWelcome ? (
                  <WelcomeScreen key="welcome" />
                ) : (
                  <motion.div
                    key="messages"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Box sx={{ py: 2 }}>
              {messages.map((message) => (
                        <AGUIErrorBoundary key={message.id}>
                <AGUIMessage
                  message={message}
                  agentState={agentState}
                            isStreaming={isRunning && message.id === messages[messages.length - 1]?.id}
                          />
                        </AGUIErrorBoundary>
                      ))}
                      
                      {isRunning && (
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3 }}
                        >
                          <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 3 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Avatar sx={{
                                width: 32,
                                height: 32,
                                background: MODE_CONFIG[queryMode].gradient
                              }}>
                                <BotIcon sx={{ fontSize: '1rem' }} />
                              </Avatar>
                              <Paper sx={{
                                px: 3,
                                py: 2,
                                borderRadius: 3,
                                backgroundColor: 'grey.50'
                              }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <CircularProgress size={16} />
                                  <Typography variant="body2" color="text.secondary">
                                    Thinking...
                                  </Typography>
                                </Box>
                              </Paper>
                            </Box>
                          </Box>
                        </motion.div>
                      )}

            <div ref={messagesEndRef} />
                    </Box>
                  </motion.div>
                )}
              </AnimatePresence>
            </Container>
          </Box>

          {/* Error Alert */}
          <Collapse in={!!error}>
            <Container maxWidth="md">
              <Alert 
                severity="error" 
                onClose={() => setError(null)}
                sx={{ mb: 2, borderRadius: 2 }}
              >
                {error}
              </Alert>
            </Container>
          </Collapse>

          {/* Input Area */}
          <Box sx={{
            backgroundColor: 'background.paper',
            p: 3
          }}>
            <Container maxWidth="md">
              <Box sx={{ position: 'relative' }}>
              <TextField
                  ref={inputRef}
                fullWidth
                  multiline
                  maxRows={4}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyPress={handleKeyPress}
                  placeholder={`Message Digital Twin AI${queryMode === QUERY_MODES.MCP ? ' (MCP Mode)' : ' (LLM Mode)'}`}
                  disabled={!isInitialized || isRunning}
                sx={{
                  '& .MuiOutlinedInput-root': {
                      backgroundColor: '#e0e0e0',
                      pr: 6,
                      fontSize: '1rem',
                      lineHeight: 1.5
                  }
                }}
              />
                
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  style={{
                    position: 'absolute',
                    right: 8,
                    bottom: 8
                  }}
                >
              <IconButton
                onClick={handleSend}
                    disabled={!input.trim() || !isInitialized || isRunning}
                sx={{
                      background: input.trim() ? MODE_CONFIG[queryMode].gradient : 'grey.300',
                  color: 'white',
                      width: 40,
                      height: 40,
                      '&:hover': {
                        background: input.trim() ? MODE_CONFIG[queryMode].gradient : 'grey.400',
                        transform: 'scale(1.05)'
                      },
                      '&:disabled': {
                        background: 'grey.300',
                        color: 'grey.500'
                      }
                    }}
                  >
                    <SendIcon sx={{ fontSize: '1.2rem' }} />
              </IconButton>
                </motion.div>
            </Box>
            </Container>
          </Box>
        </Box>
      </Box>
    </ThemeProvider>
  );
};

export default ModernAGUIChatInterface;
