require('dotenv').config();

const express = require("express");
const cors = require("cors");
const bodyParser = require("body-parser");
const Docker = require("dockerode");
const routes = require("./routes/routes");
const fs = require('fs');
const path = require('path');

// Import MCP module
const mcp = require('./mcp');

// Multer is now configured in routes.js
const templatesFolder = path.join(__dirname, 'templates');

const app = express();
const server = require('http').createServer(app);
const docker = new Docker();
app.use(cors({ origin: "*" }));
app.use(bodyParser.json({ limit: '50mb' })); // Increase limit to handle large conversation history
app.use(bodyParser.urlencoded({ limit: '50mb', extended: true })); // Also increase URL encoding limit
console.log(`Template path: ${templatesFolder}`);  // Log the template path

if (!fs.existsSync(templatesFolder)) {
    fs.mkdirSync(templatesFolder);
}

// Routes
app.use("/api", routes);

// Legacy MCP routes removed - using AG-UI only

// Mount AG-UI routes
const aguiRoutes = require('./routes/agui');
app.use('/api/agui', aguiRoutes);

// Initialize MCP client when the server starts
mcp.initialize().then((success) => {
  if (success) {
    console.log('MCP client initialized successfully');
    console.log('Using MCP server for InfluxDB access');

    // Log the available tools
    if (typeof mcp.getTools === 'function') {
      const tools = mcp.getTools();
      if (tools && tools.length > 0) {
        console.log('Available tools:', tools.map(tool => tool.name).join(', '));
      }
    } else if (mcp.bridge && typeof mcp.bridge.getTools === 'function') {
      const tools = mcp.bridge.getTools();
      if (tools && tools.length > 0) {
        console.log('Available tools:', tools.map(tool => tool.name).join(', '));
      }
    }
  } else {
    console.error('Failed to initialize MCP client');
    console.error('MCP server is required for this application to function');
    process.exit(1); // Exit the process with an error code
  }
}).catch(error => {
  console.error('Error initializing MCP client:', error);
  console.error('MCP server is required for this application to function');
  process.exit(1); // Exit the process with an error code
});

app.get("/management", async (_req, res) => {
  try {
    const containers = await docker.listContainers({ all: true });

    const devicesList = containers.map((container) => {
      const deviceName = container.Names[0].replace('/', ''); // Use container name as device name
      const status = container.State === "running" ? "Active" : "Inactive";
      const ip = container.NetworkSettings?.Networks?.bridge?.IPAddress || "N/A";
      const image = container.Image; // Get the image name

      // Get exposed port from container
      let port = "N/A";
      if (container.Ports && container.Ports.length > 0) {
        // Find the first port binding for container port 1880
        const exposedPort = container.Ports.find(
          (port) => port.PrivatePort === 1880
        );
        if (exposedPort) {
          port = exposedPort.PublicPort; // Get the public host port
        }
      }

      // Determine if the container is a Node-RED container by checking the image
      const isNodeRed = image === "custom-nodered-image"; // Match your Node-RED image here

      return {
        id: container.Id,
        name: deviceName,
        ip: ip,
        port: port, // Return the host port
        status: status,
        image: image, // Include the image name
        isNodeRed: isNodeRed, // Add a flag to determine if this is a Node-RED container
      };
    });

    res.json(devicesList);
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch containers", error });
  }
});

// Route to delete a container
app.delete("/devices/:id", async (req, res) => {
  const containerId = req.params.id;

  try {
    const container = docker.getContainer(containerId);
    await container.stop();  // Stop the container if it's running
    await container.remove();  // Remove the container

    res.status(200).json({ message: `Container with id ${containerId} deleted` });
  } catch (error) {
    res.status(500).json({ message: "Failed to delete container", error });
  }
});

// respond to every other request
app.get('*', (_request, response) => {
  response.status(200).send({message:'default route'});
  console.log('default route');
});

const PORT = process.env.PORT || 5000
server.on('listening',()=>{
    console.log('Server is running');
});
server.listen(PORT, '0.0.0.0', () => {
    console.log(`Server running on port ${PORT}`)
})

process.on('SIGINT', async function() {
    console.log("\nCaught interrupt signal ---> closing HTTP server");
    await mcp.close();
    server.close(() => {
        console.log('HTTP server closed');
        process.exit(0);
    });
});

process.on('SIGTERM', async function() {
    console.log("\nCaught terminate signal ---> closing HTTP server");
    await mcp.close();
    server.close(() => {
        console.log('HTTP server closed');
        process.exit(0);
    });
});