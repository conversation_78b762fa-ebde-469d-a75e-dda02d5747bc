/**
 * Dynamic MCP Bridge
 * Enhanced bridge that uses the JSON-configuration-based server manager
 * Provides backward compatibility while supporting dynamic server management
 */

const MCPServerManager = require('../enhanced/server-manager');
const llmService = require('../llm/service');
const path = require('path');

class DynamicMCPBridge {
  constructor() {
    this.serverManager = null;
    this.isInitialized = false;
    this.configPath = null;
  }

  /**
   * Initialize the dynamic MCP bridge
   * @param {string} configPath - Optional path to configuration file
   * @returns {Promise<boolean>} Whether initialization was successful
   */
  async initialize(configPath = null) {
    try {
      console.log('Initializing Dynamic MCP Bridge...');
      
      // Create server manager
      this.serverManager = new MCPServerManager(configPath);
      this.configPath = configPath;

      // Set up event listeners
      this.setupEventListeners();

      // Initialize server manager
      const success = await this.serverManager.initialize(configPath);
      
      if (success) {
        this.isInitialized = true;
        console.log('Dynamic MCP Bridge initialized successfully');
        
        // Log available servers and tools
        const stats = this.serverManager.getServerStats();
        console.log(`Active servers: ${stats.activeServers}/${stats.totalServers}`);
        console.log(`Total tools available: ${stats.totalTools}`);
        
        return true;
      } else {
        console.error('Failed to initialize any MCP servers');
        return false;
      }
    } catch (error) {
      console.error('Error initializing Dynamic MCP Bridge:', error);
      this.isInitialized = false;
      return false;
    }
  }

  /**
   * Set up event listeners for server manager
   */
  setupEventListeners() {
    this.serverManager.on('serverConnected', ({ serverId, server }) => {
      console.log(`Server connected: ${server.name} (${serverId})`);
    });

    this.serverManager.on('serverDisconnected', ({ serverId, server }) => {
      console.log(`Server disconnected: ${server.name} (${serverId})`);
    });

    this.serverManager.on('serverError', ({ serverId, server, error }) => {
      console.error(`Server error for ${server.name} (${serverId}):`, error.message);
    });

    this.serverManager.on('serverConnectionFailed', ({ serverId, server, error }) => {
      console.warn(`Failed to connect to ${server.name} (${serverId}):`, error.message);
    });
  }

  /**
   * Get the list of available tools from all servers
   * @returns {Array} List of tools with server information
   */
  getTools() {
    if (!this.isInitialized || !this.serverManager) {
      throw new Error('Dynamic MCP Bridge is not initialized');
    }

    return this.serverManager.getAllTools();
  }

  /**
   * Call a tool on the best available server
   * @param {string} toolName - Name of the tool to call
   * @param {Object} params - Parameters for the tool
   * @param {Array} requiredCapabilities - Required server capabilities
   * @returns {Promise<Object>} Result of the tool call
   */
  async callTool(toolName, params, requiredCapabilities = []) {
    if (!this.isInitialized || !this.serverManager) {
      throw new Error('Dynamic MCP Bridge is not initialized');
    }

    try {
      console.log(`Calling tool ${toolName} with params:`, params);
      
      const result = await this.serverManager.callTool(toolName, params, requiredCapabilities);
      
      console.log(`Tool ${toolName} executed successfully on server ${result.serverId}`);
      return result;
      
    } catch (error) {
      console.error(`Error calling tool ${toolName}:`, error);
      throw error;
    }
  }

  /**
   * Process a query using the LLM service layer
   * @param {string} query - Natural language query
   * @returns {Promise<Object>} Processed response
   */
  async processQuery(query) {
    if (!this.isInitialized || !this.serverManager) {
      throw new Error('Dynamic MCP Bridge is not initialized');
    }

    try {
      console.log(`Processing query: "${query}"`);
      
      // Use the LLM service with the server manager as the tool client
      const result = await llmService.processQuery(query, this.serverManager);
      
      return result;
    } catch (error) {
      console.error('Error processing query:', error);
      return {
        type: 'error',
        content: `Error processing query: ${error.message}`
      };
    }
  }

  /**
   * Get server statistics
   * @returns {Object} Server statistics
   */
  getServerStats() {
    if (!this.isInitialized || !this.serverManager) {
      return { totalServers: 0, activeServers: 0, totalTools: 0 };
    }

    return this.serverManager.getServerStats();
  }

  /**
   * Add a new server dynamically
   * @param {Object} serverConfig - Server configuration
   * @returns {Promise<Object>} Server info
   */
  async addServer(serverConfig) {
    if (!this.isInitialized || !this.serverManager) {
      throw new Error('Dynamic MCP Bridge is not initialized');
    }

    return this.serverManager.addServer(serverConfig);
  }

  /**
   * Remove a server
   * @param {string} serverId - Server ID
   * @returns {Promise<boolean>} Whether removal was successful
   */
  async removeServer(serverId) {
    if (!this.isInitialized || !this.serverManager) {
      throw new Error('Dynamic MCP Bridge is not initialized');
    }

    return this.serverManager.removeServer(serverId);
  }

  /**
   * Reload configuration
   * @param {string} configPath - Optional new config path
   * @returns {Promise<boolean>} Whether reload was successful
   */
  async reloadConfiguration(configPath = null) {
    if (!this.serverManager) {
      return this.initialize(configPath);
    }

    const success = await this.serverManager.reloadConfiguration(configPath);
    this.isInitialized = success;
    return success;
  }

  /**
   * Get current configuration
   * @returns {Object} Current configuration
   */
  getConfiguration() {
    if (!this.serverManager) {
      return null;
    }

    return this.serverManager.getConfiguration();
  }

  /**
   * Get the active LLM
   * @returns {string} Active LLM
   */
  getActiveLLM() {
    return llmService.getActiveLLM();
  }

  /**
   * Set the active LLM
   * @param {string} llm - LLM to set as active
   * @returns {boolean} Whether the operation was successful
   */
  setActiveLLM(llm) {
    return llmService.setActiveLLM(llm);
  }

  /**
   * Check if the bridge is initialized
   * @returns {boolean} Whether the bridge is initialized
   */
  isReady() {
    return this.isInitialized && this.serverManager && this.serverManager.activeServers.size > 0;
  }

  /**
   * Close the bridge and all connections
   * @returns {Promise<void>}
   */
  async close() {
    if (this.serverManager) {
      await this.serverManager.shutdown();
    }
    this.isInitialized = false;
    console.log('Dynamic MCP Bridge closed');
  }
}

// Create and export a singleton instance
const dynamicBridge = new DynamicMCPBridge();
module.exports = dynamicBridge;
