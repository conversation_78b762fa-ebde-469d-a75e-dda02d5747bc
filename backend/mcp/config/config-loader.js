/**
 * MCP Configuration Loader
 * Loads and validates MCP server configurations from JSON files
 * Supports environment variable substitution and validation
 */

const fs = require('fs');
const path = require('path');
const Ajv = require('ajv');
const CursorFormatAdapter = require('./cursor-adapter');

class MCPConfigLoader {
  constructor() {
    this.ajv = new Ajv({ allErrors: true });
    this.schema = null;
    this.cursorAdapter = new CursorFormatAdapter();
    this.loadSchema();
  }

  /**
   * Load the JSON schema for validation
   */
  loadSchema() {
    try {
      const schemaPath = path.join(__dirname, 'server-schema.json');
      this.schema = JSON.parse(fs.readFileSync(schemaPath, 'utf8'));
      this.validate = this.ajv.compile(this.schema);
    } catch (error) {
      console.error('Failed to load MCP server schema:', error);
      throw error;
    }
  }

  /**
   * Load configuration from a JSON file
   * Supports both Enhanced format and Cursor format
   * @param {string} configPath - Path to the configuration file
   * @returns {Object} Parsed and validated configuration
   */
  loadConfig(configPath) {
    try {
      // Read the configuration file
      const configContent = fs.readFileSync(configPath, 'utf8');

      // Substitute environment variables
      const substitutedContent = this.substituteEnvironmentVariables(configContent);

      // Parse JSON
      const rawConfig = JSON.parse(substitutedContent);

      // Detect format and convert if necessary
      const config = this.detectAndConvertFormat(rawConfig);

      // Validate against schema
      this.validateConfig(config);

      // Process and normalize the configuration
      return this.processConfig(config);

    } catch (error) {
      console.error(`Failed to load MCP configuration from ${configPath}:`, error);
      throw error;
    }
  }

  /**
   * Detect configuration format and convert to Enhanced format if needed
   * @param {Object} config - Raw configuration object
   * @returns {Object} Configuration in Enhanced format
   */
  detectAndConvertFormat(config) {
    // Check if it's Cursor format
    if (config.mcpServers && !config.servers) {
      console.log('Detected Cursor/Claude Desktop format, converting to Enhanced format...');
      return this.cursorAdapter.cursorToEnhanced(config);
    }

    // Check if it's Enhanced format
    if (config.servers || config.globalSettings) {
      console.log('Detected Enhanced format');
      return config;
    }

    // Unknown format
    throw new Error('Unknown configuration format. Expected either Enhanced format (with "servers" array) or Cursor format (with "mcpServers" object)');
  }

  /**
   * Substitute environment variables in the configuration
   * Supports ${VAR_NAME} syntax
   * @param {string} content - Configuration content
   * @returns {string} Content with substituted variables
   */
  substituteEnvironmentVariables(content) {
    return content.replace(/\$\{([^}]+)\}/g, (match, varName) => {
      const value = process.env[varName];
      if (value === undefined) {
        console.warn(`Environment variable ${varName} is not set, using empty string`);
        return '';
      }
      return value;
    });
  }

  /**
   * Validate configuration against the schema
   * @param {Object} config - Configuration object
   * @throws {Error} If validation fails
   */
  validateConfig(config) {
    const valid = this.validate(config);
    if (!valid) {
      const errors = this.validate.errors.map(error => 
        `${error.instancePath}: ${error.message}`
      ).join(', ');
      throw new Error(`Configuration validation failed: ${errors}`);
    }
  }

  /**
   * Process and normalize the configuration
   * @param {Object} config - Raw configuration
   * @returns {Object} Processed configuration
   */
  processConfig(config) {
    const processed = {
      globalSettings: this.processGlobalSettings(config.globalSettings || {}),
      servers: config.servers.map(server => this.processServerConfig(server))
    };

    return processed;
  }

  /**
   * Process global settings with defaults
   * @param {Object} settings - Global settings
   * @returns {Object} Processed global settings
   */
  processGlobalSettings(settings) {
    return {
      loadBalancing: settings.loadBalancing || 'priority',
      failover: settings.failover !== false,
      healthMonitoring: settings.healthMonitoring !== false,
      logging: {
        level: settings.logging?.level || 'info',
        enableMetrics: settings.logging?.enableMetrics !== false
      }
    };
  }

  /**
   * Process individual server configuration
   * @param {Object} server - Server configuration
   * @returns {Object} Processed server configuration
   */
  processServerConfig(server) {
    const processed = {
      ...server,
      enabled: server.enabled !== false,
      capabilities: server.capabilities || [],
      tools: server.tools || [],
      metadata: {
        version: server.metadata?.version || '1.0.0',
        priority: server.metadata?.priority || 1,
        tags: server.metadata?.tags || [],
        maintainer: server.metadata?.maintainer || 'Unknown',
        documentation: server.metadata?.documentation || '',
        ...server.metadata
      },
      healthCheck: {
        enabled: server.healthCheck?.enabled !== false,
        interval: server.healthCheck?.interval || 30000,
        timeout: server.healthCheck?.timeout || 5000,
        method: server.healthCheck?.method || 'listTools',
        ...server.healthCheck
      }
    };

    // Process authentication environment variables
    if (processed.authentication?.envVars) {
      processed.authentication.processedEnvVars = {};
      processed.authentication.envVars.forEach(envVar => {
        if (envVar.fromEnv) {
          processed.authentication.processedEnvVars[envVar.name] = 
            process.env[envVar.fromEnv] || envVar.value || '';
        } else {
          processed.authentication.processedEnvVars[envVar.name] = envVar.value || '';
        }
      });
    }

    return processed;
  }

  /**
   * Load multiple configuration files and merge them
   * @param {Array<string>} configPaths - Array of configuration file paths
   * @returns {Object} Merged configuration
   */
  loadMultipleConfigs(configPaths) {
    const configs = configPaths.map(path => this.loadConfig(path));
    
    // Merge configurations
    const merged = {
      globalSettings: configs[0].globalSettings,
      servers: []
    };

    configs.forEach(config => {
      merged.servers.push(...config.servers);
    });

    // Remove duplicate servers (by ID)
    const uniqueServers = new Map();
    merged.servers.forEach(server => {
      if (!uniqueServers.has(server.id)) {
        uniqueServers.set(server.id, server);
      } else {
        console.warn(`Duplicate server ID found: ${server.id}, skipping...`);
      }
    });

    merged.servers = Array.from(uniqueServers.values());
    
    return merged;
  }

  /**
   * Get default configuration path
   * @returns {string} Default configuration file path
   */
  getDefaultConfigPath() {
    return path.join(__dirname, 'servers.json');
  }

  /**
   * Load configuration from default location
   * @returns {Object} Configuration object
   */
  loadDefaultConfig() {
    return this.loadConfig(this.getDefaultConfigPath());
  }

  /**
   * Validate a server configuration object
   * @param {Object} serverConfig - Server configuration
   * @returns {boolean} Whether the configuration is valid
   */
  validateServerConfig(serverConfig) {
    try {
      const tempConfig = { servers: [serverConfig] };
      this.validateConfig(tempConfig);
      return true;
    } catch (error) {
      console.error('Server configuration validation failed:', error);
      return false;
    }
  }

  /**
   * Load Cursor format configuration
   * @param {string} configPath - Path to Cursor format file
   * @returns {Object} Enhanced format configuration
   */
  loadCursorConfig(configPath) {
    return this.cursorAdapter.loadCursorConfig(configPath);
  }

  /**
   * Convert Enhanced format to Cursor format
   * @param {Object} enhancedConfig - Enhanced format configuration
   * @returns {Object} Cursor format configuration
   */
  convertToCursorFormat(enhancedConfig) {
    return this.cursorAdapter.enhancedToCursor(enhancedConfig);
  }

  /**
   * Save configuration in Cursor format
   * @param {Object} enhancedConfig - Enhanced format configuration
   * @param {string} outputPath - Output file path
   */
  saveAsCursorFormat(enhancedConfig, outputPath) {
    this.cursorAdapter.saveAsCursorFormat(enhancedConfig, outputPath);
  }

  /**
   * Validate Cursor format configuration
   * @param {Object} config - Cursor format configuration
   * @returns {boolean} Whether the configuration is valid
   */
  validateCursorFormat(config) {
    return this.cursorAdapter.validateCursorFormat(config);
  }

  /**
   * Get supported configuration formats
   * @returns {Array} List of supported formats
   */
  getSupportedFormats() {
    return [
      {
        name: 'Enhanced',
        description: 'Full-featured format with advanced configuration options',
        example: 'servers.json'
      },
      {
        name: 'Cursor',
        description: 'Cursor/Claude Desktop compatible format',
        example: 'cursor-format.json'
      }
    ];
  }
}

module.exports = MCPConfigLoader;
