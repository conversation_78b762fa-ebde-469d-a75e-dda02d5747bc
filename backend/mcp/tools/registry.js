/**
 * Tool Registry
 * Central registry for all available MCP tools
 * Coordinates between different tool modules
 */

const influxQueries = require('./influxdb/queries');
const influxManagement = require('./influxdb/management');

/**
 * Get the list of available tools from all modules
 * @returns {Array} List of tools
 */
function getTools() {
  return [
    ...influxQueries.getQueryTools(),
    ...influxManagement.getManagementTools()
  ];
}

/**
 * Call a tool with the given parameters
 * Routes tool calls to appropriate modules
 * @param {string} toolName - Name of the tool to call
 * @param {Object} params - Parameters for the tool
 * @returns {Promise<Object>} Tool result
 */
async function callTool(toolName, params) {
  console.log(`[Tool Registry] Calling tool ${toolName} with params:`, params);

  // Route to appropriate module based on tool name
  switch (toolName) {
    // InfluxDB Query Tools
    case 'query-data':
      return await influxQueries.queryData(params);
    case 'write-data':
      return await influxQueries.writeData(params);

    // InfluxDB Management Tools
    case 'create-bucket':
      return await influxManagement.createBucket(params);
    case 'create-org':
      return await influxManagement.createOrg(params);

    default:
      throw new Error(`Unknown tool: ${toolName}`);
  }
}

/**
 * Extract content from a tool result
 * Utility function for processing tool responses
 * @param {Object} result - Tool result
 * @returns {string} Extracted content
 */
function extractContent(result) {
  if (!result) return '';

  if (result.content) {
    if (Array.isArray(result.content)) {
      return result.content.reduce((acc, item) => {
        if (item.type === 'text') {
          return acc + item.text;
        }
        return acc;
      }, '');
    } else if (typeof result.content === 'string') {
      return result.content;
    }
  }

  return '';
}

module.exports = {
  getTools,
  callTool,
  extractContent
};
