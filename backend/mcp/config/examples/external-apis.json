{"globalSettings": {"loadBalancing": "round-robin", "failover": true, "healthMonitoring": true, "logging": {"level": "debug", "enableMetrics": true}}, "servers": [{"id": "influxdb-local", "name": "Local InfluxDB Server", "description": "Local InfluxDB instance for IoT sensor data", "enabled": true, "connection": {"type": "stdio", "command": "node", "args": ["${MCP_SERVER_PATH}"], "timeout": 30000, "retryAttempts": 3, "retryDelay": 1000}, "authentication": {"type": "none", "envVars": [{"name": "INFLUXDB_TOKEN", "fromEnv": "INFLUXDB_TOKEN"}, {"name": "INFLUXDB_ORG", "fromEnv": "INFLUXDB_ORG"}, {"name": "INFLUXDB_URL", "fromEnv": "INFLUXDB_URL"}]}, "capabilities": ["time-series", "iot-data", "local-storage"], "metadata": {"priority": 10, "tags": ["local", "primary"]}, "healthCheck": {"enabled": true, "interval": 30000}}, {"id": "weather-service", "name": "OpenWeatherMap API", "description": "External weather data service for environmental context", "enabled": false, "connection": {"type": "stdio", "command": "node", "args": ["${MCP_WEATHER_SERVER_PATH}"], "timeout": 15000, "retryAttempts": 2, "retryDelay": 2000}, "authentication": {"type": "none", "envVars": [{"name": "WEATHER_API_KEY", "fromEnv": "OPENWEATHER_API_KEY"}, {"name": "WEATHER_BASE_URL", "value": "https://api.openweathermap.org/data/2.5"}]}, "capabilities": ["weather-data", "environmental", "external-api", "real-time"], "tools": [{"name": "get-current-weather", "description": "Get current weather conditions", "category": "environmental"}, {"name": "get-weather-forecast", "description": "Get weather forecast", "category": "environmental"}, {"name": "get-historical-weather", "description": "Get historical weather data", "category": "environmental"}], "metadata": {"version": "2.5.0", "priority": 6, "tags": ["weather", "external", "api", "environmental"], "maintainer": "OpenWeatherMap", "documentation": "https://openweathermap.org/api"}, "healthCheck": {"enabled": true, "interval": 300000, "timeout": 10000, "method": "ping"}}, {"id": "news-service", "name": "News API Service", "description": "External news service for contextual information", "enabled": false, "connection": {"type": "stdio", "command": "node", "args": ["${MCP_NEWS_SERVER_PATH}"], "timeout": 20000, "retryAttempts": 2, "retryDelay": 3000}, "authentication": {"type": "none", "envVars": [{"name": "NEWS_API_KEY", "fromEnv": "NEWS_API_KEY"}, {"name": "NEWS_BASE_URL", "value": "https://newsapi.org/v2"}]}, "capabilities": ["news-data", "external-api", "contextual-info"], "tools": [{"name": "get-top-headlines", "description": "Get top news headlines", "category": "information"}, {"name": "search-news", "description": "Search for specific news articles", "category": "information"}], "metadata": {"version": "1.0.0", "priority": 3, "tags": ["news", "external", "api", "contextual"], "maintainer": "News API", "documentation": "https://newsapi.org/docs"}, "healthCheck": {"enabled": true, "interval": 600000, "timeout": 15000, "method": "ping"}}, {"id": "geocoding-service", "name": "Geocoding API Service", "description": "Location and geocoding services", "enabled": false, "connection": {"type": "stdio", "command": "node", "args": ["${MCP_GEOCODING_SERVER_PATH}"], "timeout": 10000, "retryAttempts": 2, "retryDelay": 1000}, "authentication": {"type": "none", "envVars": [{"name": "GEOCODING_API_KEY", "fromEnv": "GEOCODING_API_KEY"}]}, "capabilities": ["geocoding", "location-services", "external-api"], "tools": [{"name": "geocode-address", "description": "Convert address to coordinates", "category": "location"}, {"name": "reverse-geocode", "description": "Convert coordinates to address", "category": "location"}], "metadata": {"version": "1.0.0", "priority": 4, "tags": ["geocoding", "location", "external", "api"], "maintainer": "Geocoding Service Provider"}, "healthCheck": {"enabled": true, "interval": 300000, "timeout": 8000, "method": "ping"}}]}